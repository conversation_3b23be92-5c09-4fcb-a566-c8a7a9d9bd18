{"cells": [{"cell_type": "code", "execution_count": 2, "id": "313ede3b", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from dotenv import load_dotenv\n", "load_dotenv()\n"]}, {"cell_type": "code", "execution_count": 3, "id": "321f3a07", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>客户id</th>\n", "      <th>客户名称</th>\n", "      <th>用量分层</th>\n", "      <th>手机号</th>\n", "      <th>销售大区</th>\n", "      <th>bd名称</th>\n", "      <th>bd_m2</th>\n", "      <th>bd_m1</th>\n", "      <th>四级类目数</th>\n", "      <th>TOP1用量品类</th>\n", "      <th>...</th>\n", "      <th>鲜牛奶.1</th>\n", "      <th>糯米粉.1</th>\n", "      <th>冷冻熟蔬菜制品.1</th>\n", "      <th>果糖.1</th>\n", "      <th>马斯卡彭.1</th>\n", "      <th>切达再制干酪.1</th>\n", "      <th>水果罐头.1</th>\n", "      <th>水果类馅料.1</th>\n", "      <th>冷冻果肉.1</th>\n", "      <th>芋圆.1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>12</td>\n", "      <td>FeelingsCake</td>\n", "      <td>中用量(60&lt;=月用量&lt;165KG)</td>\n", "      <td>13567196607</td>\n", "      <td>浙江大区</td>\n", "      <td>严红坤</td>\n", "      <td>翟远方</td>\n", "      <td>李钱程</td>\n", "      <td>4</td>\n", "      <td>常温牛奶</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>29</td>\n", "      <td>甜包谷</td>\n", "      <td>高用量(月用量&gt;=165KG)</td>\n", "      <td>13575460448</td>\n", "      <td>浙江大区</td>\n", "      <td>袁自超</td>\n", "      <td>翟远方</td>\n", "      <td>李钱程</td>\n", "      <td>10</td>\n", "      <td>高筋面粉</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>9.91815</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>13.914467</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>55</td>\n", "      <td>333手工蛋糕</td>\n", "      <td>高用量(月用量&gt;=165KG)</td>\n", "      <td>15268197285</td>\n", "      <td>浙江大区</td>\n", "      <td>严红坤</td>\n", "      <td>翟远方</td>\n", "      <td>李钱程</td>\n", "      <td>5</td>\n", "      <td>搅打型稀奶油</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3 rows × 70 columns</p>\n", "</div>"], "text/plain": ["   客户id          客户名称                用量分层          手机号  销售大区 bd名称 bd_m2 bd_m1  \\\n", "0    12  FeelingsCake  中用量(60<=月用量<165KG)  13567196607  浙江大区  严红坤   翟远方   李钱程   \n", "1    29           甜包谷     高用量(月用量>=165KG)  13575460448  浙江大区  袁自超   翟远方   李钱程   \n", "2    55       333手工蛋糕     高用量(月用量>=165KG)  15268197285  浙江大区  严红坤   翟远方   李钱程   \n", "\n", "   四级类目数 TOP1用量品类  ...  鲜牛奶.1 糯米粉.1  冷冻熟蔬菜制品.1 果糖.1  马斯卡彭.1 切达再制干酪.1  水果罐头.1  \\\n", "0      4     常温牛奶  ...    0.0   0.0        0.0  0.0     0.0  0.00000     0.0   \n", "1     10     高筋面粉  ...    0.0   0.0        0.0  0.0     0.0  9.91815     0.0   \n", "2      5   搅打型稀奶油  ...    0.0   0.0        0.0  0.0     0.0  0.00000     0.0   \n", "\n", "   水果类馅料.1  冷冻果肉.1       芋圆.1  \n", "0      0.0     0.0   0.000000  \n", "1      0.0     0.0  13.914467  \n", "2      0.0     0.0   0.000000  \n", "\n", "[3 rows x 70 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import os\n", "\n", "cust_df=pd.read_excel(\"./客户用量数据明细.xlsx\", \"客户明细\", header=1)\n", "cust_df.head(3)"]}, {"cell_type": "code", "execution_count": 4, "id": "780995cb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>客户id</th>\n", "      <th>客户名称</th>\n", "      <th>用量分层</th>\n", "      <th>手机号</th>\n", "      <th>销售大区</th>\n", "      <th>bd_name</th>\n", "      <th>bd_m2</th>\n", "      <th>bd_m1</th>\n", "      <th>四级类目数</th>\n", "      <th>TOP1用量品类</th>\n", "      <th>...</th>\n", "      <th>预估提升量_鲜牛奶</th>\n", "      <th>预估提升量_糯米粉</th>\n", "      <th>预估提升量_冷冻熟蔬菜制品</th>\n", "      <th>预估提升量_果糖</th>\n", "      <th>预估提升量_马斯卡彭</th>\n", "      <th>预估提升量_切达再制干酪</th>\n", "      <th>预估提升量_水果罐头</th>\n", "      <th>预估提升量_水果类馅料</th>\n", "      <th>预估提升量_冷冻果肉</th>\n", "      <th>预估提升量_芋圆</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>12</td>\n", "      <td>FeelingsCake</td>\n", "      <td>中用量(60&lt;=月用量&lt;165KG)</td>\n", "      <td>13567196607</td>\n", "      <td>浙江大区</td>\n", "      <td>严红坤</td>\n", "      <td>翟远方</td>\n", "      <td>李钱程</td>\n", "      <td>4</td>\n", "      <td>常温牛奶</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>29</td>\n", "      <td>甜包谷</td>\n", "      <td>高用量(月用量&gt;=165KG)</td>\n", "      <td>13575460448</td>\n", "      <td>浙江大区</td>\n", "      <td>袁自超</td>\n", "      <td>翟远方</td>\n", "      <td>李钱程</td>\n", "      <td>10</td>\n", "      <td>高筋面粉</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>9.91815</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>13.914467</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>55</td>\n", "      <td>333手工蛋糕</td>\n", "      <td>高用量(月用量&gt;=165KG)</td>\n", "      <td>15268197285</td>\n", "      <td>浙江大区</td>\n", "      <td>严红坤</td>\n", "      <td>翟远方</td>\n", "      <td>李钱程</td>\n", "      <td>5</td>\n", "      <td>搅打型稀奶油</td>\n", "      <td>...</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.00000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3 rows × 70 columns</p>\n", "</div>"], "text/plain": ["   客户id          客户名称                用量分层          手机号  销售大区 bd_name bd_m2  \\\n", "0    12  FeelingsCake  中用量(60<=月用量<165KG)  13567196607  浙江大区     严红坤   翟远方   \n", "1    29           甜包谷     高用量(月用量>=165KG)  13575460448  浙江大区     袁自超   翟远方   \n", "2    55       333手工蛋糕     高用量(月用量>=165KG)  15268197285  浙江大区     严红坤   翟远方   \n", "\n", "  bd_m1  四级类目数 TOP1用量品类  ...  预估提升量_鲜牛奶 预估提升量_糯米粉  预估提升量_冷冻熟蔬菜制品 预估提升量_果糖  \\\n", "0   李钱程      4     常温牛奶  ...        0.0       0.0            0.0      0.0   \n", "1   李钱程     10     高筋面粉  ...        0.0       0.0            0.0      0.0   \n", "2   李钱程      5   搅打型稀奶油  ...        0.0       0.0            0.0      0.0   \n", "\n", "   预估提升量_马斯卡彭 预估提升量_切达再制干酪  预估提升量_水果罐头  预估提升量_水果类馅料  预估提升量_冷冻果肉   预估提升量_芋圆  \n", "0         0.0      0.00000         0.0          0.0         0.0   0.000000  \n", "1         0.0      9.91815         0.0          0.0         0.0  13.914467  \n", "2         0.0      0.00000         0.0          0.0         0.0   0.000000  \n", "\n", "[3 rows x 70 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["cust_df.columns = ['客户id', '客户名称', '用量分层', '手机号', '销售大区', 'bd_name', 'bd_m2', 'bd_m1',\n", "       '四级类目数', 'TOP1用量品类', 'TOP1占比', 'TOP2品类', 'TOP2品类占比', 'TOP3品类',\n", "       'TOP3品类占比', '客户用量类型', '实际用量_搅打型稀奶油', '实际用量_常温牛奶', '实际用量_白砂糖', '实际用量_高筋面粉', '实际用量_低筋面粉', '实际用量_无盐黄油',\n", "       '实际用量_植脂奶油', '实际用量_其他型碳酸饮料', '实际用量_奶油奶酪', '实际用量_全脂乳粉', '实际用量_饼干碎', '实际用量_罐装炼乳', '实际用量_中筋面粉', '实际用量_果汁原浆',\n", "       '实际用量_马苏里拉', '实际用量_波波丨晶球', '实际用量_新鲜蛋类', '实际用量_鲜牛奶', '实际用量_糯米粉', '实际用量_冷冻熟蔬菜制品', '实际用量_果糖', '实际用量_马斯卡彭',\n", "       '实际用量_切达再制干酪', '实际用量_水果罐头', '实际用量_水果类馅料', '实际用量_冷冻果肉', '实际用量_芋圆', '预估提升量_搅打型稀奶油', '预估提升量_常温牛奶', '预估提升量_白砂糖',\n", "       '预估提升量_高筋面粉', '预估提升量_低筋面粉', '预估提升量_无盐黄油', '预估提升量_植脂奶油', '预估提升量_其他型碳酸饮料', '预估提升量_奶油奶酪', '预估提升量_全脂乳粉',\n", "       '预估提升量_饼干碎', '预估提升量_罐装炼乳', '预估提升量_中筋面粉', '预估提升量_果汁原浆', '预估提升量_马苏里拉', '预估提升量_波波丨晶球', '预估提升量_新鲜蛋类',\n", "       '预估提升量_鲜牛奶', '预估提升量_糯米粉', '预估提升量_冷冻熟蔬菜制品', '预估提升量_果糖', '预估提升量_马斯卡彭', '预估提升量_切达再制干酪', '预估提升量_水果罐头',\n", "       '预估提升量_水果类馅料', '预估提升量_冷冻果肉', '预估提升量_芋圆']\n", "\n", "cust_df.head(3)"]}, {"cell_type": "code", "execution_count": 12, "id": "c2f1d1e4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   客户id  cnt min(客户名称)     max(客户名称)\n", "0  4119    2    炮皮家烘焙店          艾可烘焙\n", "1  9731    2       程程家  程程家帮忙搬到二楼冷藏室\n"]}], "source": ["from pandasql import sqldf\n", "\n", "dupelicate_df=sqldf(\"select 客户id,count(0) cnt ,min(客户名称),max(客户名称) from cust_df group by 客户id having cnt>1\") \n", "print(dupelicate_df.head(2))\n", "\n", "cust_df.drop_duplicates(subset=['客户id'], keep='first', inplace=True)"]}, {"cell_type": "code", "execution_count": 14, "id": "d36378e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generated batch 1: 1000/12133 records (1000 rows in this batch)\n", "Generated batch 2: 2000/12133 records (1000 rows in this batch)\n", "Generated batch 3: 3000/12133 records (1000 rows in this batch)\n", "Generated batch 4: 4000/12133 records (1000 rows in this batch)\n", "Generated batch 5: 5000/12133 records (1000 rows in this batch)\n", "Generated batch 6: 6000/12133 records (1000 rows in this batch)\n", "Generated batch 7: 7000/12133 records (1000 rows in this batch)\n", "Generated batch 8: 8000/12133 records (1000 rows in this batch)\n", "Generated batch 9: 9000/12133 records (1000 rows in this batch)\n", "Generated batch 10: 10000/12133 records (1000 rows in this batch)\n", "Generated batch 11: 11000/12133 records (1000 rows in this batch)\n", "Generated batch 12: 12000/12133 records (1000 rows in this batch)\n", "Generated batch 13: 12133/12133 records (133 rows in this batch)\n", "SQL file generated successfully: customer_usage_insert.sql\n", "Total records processed: 12133\n", "Number of INSERT statements: 13\n", "Records per statement: 1000\n", "Columns mapped: 70\n", "Performance optimization: Using batch INSERT with 1000 rows per statement\n", "\n", "Sample DataFrame columns:\n", "['客户id', '客户名称', '用量分层', '手机号', '销售大区', 'bd_name', 'bd_m2', 'bd_m1', '四级类目数', 'TOP1用量品类', 'TOP1占比', 'TOP2品类', 'TOP2品类占比', 'TOP3品类', 'TOP3品类占比', '客户用量类型', '实际用量_搅打型稀奶油', '实际用量_常温牛奶', '实际用量_白砂糖', '实际用量_高筋面粉', '实际用量_低筋面粉', '实际用量_无盐黄油', '实际用量_植脂奶油', '实际用量_其他型碳酸饮料', '实际用量_奶油奶酪', '实际用量_全脂乳粉', '实际用量_饼干碎', '实际用量_罐装炼乳', '实际用量_中筋面粉', '实际用量_果汁原浆', '实际用量_马苏里拉', '实际用量_波波丨晶球', '实际用量_新鲜蛋类', '实际用量_鲜牛奶', '实际用量_糯米粉', '实际用量_冷冻熟蔬菜制品', '实际用量_果糖', '实际用量_马斯卡彭', '实际用量_切达再制干酪', '实际用量_水果罐头', '实际用量_水果类馅料', '实际用量_冷冻果肉', '实际用量_芋圆', '预估提升量_搅打型稀奶油', '预估提升量_常温牛奶', '预估提升量_白砂糖', '预估提升量_高筋面粉', '预估提升量_低筋面粉', '预估提升量_无盐黄油', '预估提升量_植脂奶油', '预估提升量_其他型碳酸饮料', '预估提升量_奶油奶酪', '预估提升量_全脂乳粉', '预估提升量_饼干碎', '预估提升量_罐装炼乳', '预估提升量_中筋面粉', '预估提升量_果汁原浆', '预估提升量_马苏里拉', '预估提升量_波波丨晶球', '预估提升量_新鲜蛋类', '预估提升量_鲜牛奶', '预估提升量_糯米粉', '预估提升量_冷冻熟蔬菜制品', '预估提升量_果糖', '预估提升量_马斯卡彭', '预估提升量_切达再制干酪', '预估提升量_水果罐头', '预估提升量_水果类馅料', '预估提升量_冷冻果肉', '预估提升量_芋圆']\n", "\n", "Sample DataFrame shape: (12133, 70)\n", "\n", "First few rows:\n", "   客户id          客户名称                用量分层          手机号  销售大区 bd_name bd_m2  \\\n", "0    12  FeelingsCake  中用量(60<=月用量<165KG)  13567196607  浙江大区     严红坤   翟远方   \n", "1    29           甜包谷     高用量(月用量>=165KG)  13575460448  浙江大区     袁自超   翟远方   \n", "2    55       333手工蛋糕     高用量(月用量>=165KG)  15268197285  浙江大区     严红坤   翟远方   \n", "\n", "  bd_m1  四级类目数 TOP1用量品类  ...  预估提升量_鲜牛奶 预估提升量_糯米粉  预估提升量_冷冻熟蔬菜制品 预估提升量_果糖  \\\n", "0   李钱程      4     常温牛奶  ...        0.0       0.0            0.0      0.0   \n", "1   李钱程     10     高筋面粉  ...        0.0       0.0            0.0      0.0   \n", "2   李钱程      5   搅打型稀奶油  ...        0.0       0.0            0.0      0.0   \n", "\n", "   预估提升量_马斯卡彭 预估提升量_切达再制干酪  预估提升量_水果罐头  预估提升量_水果类馅料  预估提升量_冷冻果肉   预估提升量_芋圆  \n", "0         0.0      0.00000         0.0          0.0         0.0   0.000000  \n", "1         0.0      9.91815         0.0          0.0         0.0  13.914467  \n", "2         0.0      0.00000         0.0          0.0         0.0   0.000000  \n", "\n", "[3 rows x 70 columns]\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from typing import Any, Optional\n", "import re\n", "\n", "def generate_sql_insert_file(df: pd.DataFrame, output_file: str = 'insert_statements.sql', \n", "                        table_name: str = 'customer_usage_data', batch_size: int = 1000):\n", "    \"\"\"\n", "    Generate SQL INSERT statements from a pandas DataFrame with Chinese column names\n", "    and save them to a SQL file. Uses batch INSERT with 1000 merchants per statement for optimal performance.\n", "\n", "    Args:\n", "        df: pandas DataFrame with Chinese column names\n", "        output_file: Output SQL file path\n", "        table_name: Target table name\n", "        batch_size: Number of records per INSERT statement (default: 1000 for optimal performance)\n", "    \"\"\"\n", "\n", "    # Column mapping from Chinese to English\n", "    column_mapping = {\n", "        '客户id': 'm_id',\n", "        '客户名称': 'm_name',\n", "        '用量分层': 'usage_tier',\n", "        '手机号': 'phone_number',\n", "        '销售大区': 'sales_region',\n", "        'bd_name': 'bd_name',\n", "        'bd_m2': 'bd_m2',\n", "        'bd_m1': 'bd_m1',\n", "        '四级类目数': 'four_level_category_count',\n", "        'TOP1用量品类': 'top1_usage_category',\n", "        'TOP1占比': 'top1_percentage',\n", "        'TOP2品类': 'top2_category',\n", "        'TOP2品类占比': 'top2_percentage',\n", "        'TOP3品类': 'top3_category',\n", "        'TOP3品类占比': 'top3_percentage',\n", "        '客户用量类型': 'customer_usage_type',\n", "        '实际用量_搅打型稀奶油': 'actual_whipped_cream',\n", "        '实际用量_常温牛奶': 'actual_room_temp_milk',\n", "        '实际用量_白砂糖': 'actual_white_sugar',\n", "        '实际用量_高筋面粉': 'actual_high_gluten_flour',\n", "        '实际用量_低筋面粉': 'actual_low_gluten_flour',\n", "        '实际用量_无盐黄油': 'actual_unsalted_butter',\n", "        '实际用量_植脂奶油': 'actual_vegetable_cream',\n", "        '实际用量_其他型碳酸饮料': 'actual_carbonated_drinks',\n", "        '实际用量_奶油奶酪': 'actual_cream_cheese',\n", "        '实际用量_全脂乳粉': 'actual_whole_milk_powder',\n", "        '实际用量_饼干碎': 'actual_cookie_crumbs',\n", "        '实际用量_罐装炼乳': 'actual_canned_condensed_milk',\n", "        '实际用量_中筋面粉': 'actual_medium_gluten_flour',\n", "        '实际用量_果汁原浆': 'actual_fruit_juice_concentrate',\n", "        '实际用量_马苏里拉': 'actual_mozzarella',\n", "        '实际用量_波波丨晶球': 'actual_popping_boba',\n", "        '实际用量_新鲜蛋类': 'actual_fresh_eggs',\n", "        '实际用量_鲜牛奶': 'actual_fresh_milk',\n", "        '实际用量_糯米粉': 'actual_glutinous_rice_flour',\n", "        '实际用量_冷冻熟蔬菜制品': 'actual_frozen_cooked_vegetables',\n", "        '实际用量_果糖': 'actual_fructose',\n", "        '实际用量_马斯卡彭': 'actual_mascarpone',\n", "        '实际用量_切达再制干酪': 'actual_cheddar_processed_cheese',\n", "        '实际用量_水果罐头': 'actual_canned_fruits',\n", "        '实际用量_水果类馅料': 'actual_fruit_fillings',\n", "        '实际用量_冷冻果肉': 'actual_frozen_fruit_pulp',\n", "        '实际用量_芋圆': 'actual_taro_balls',\n", "        '预估提升量_搅打型稀奶油': 'estimated_whipped_cream',\n", "        '预估提升量_常温牛奶': 'estimated_room_temp_milk',\n", "        '预估提升量_白砂糖': 'estimated_white_sugar',\n", "        '预估提升量_高筋面粉': 'estimated_high_gluten_flour',\n", "        '预估提升量_低筋面粉': 'estimated_low_gluten_flour',\n", "        '预估提升量_无盐黄油': 'estimated_unsalted_butter',\n", "        '预估提升量_植脂奶油': 'estimated_vegetable_cream',\n", "        '预估提升量_其他型碳酸饮料': 'estimated_carbonated_drinks',\n", "        '预估提升量_奶油奶酪': 'estimated_cream_cheese',\n", "        '预估提升量_全脂乳粉': 'estimated_whole_milk_powder',\n", "        '预估提升量_饼干碎': 'estimated_cookie_crumbs',\n", "        '预估提升量_罐装炼乳': 'estimated_canned_condensed_milk',\n", "        '预估提升量_中筋面粉': 'estimated_medium_gluten_flour',\n", "        '预估提升量_果汁原浆': 'estimated_fruit_juice_concentrate',\n", "        '预估提升量_马苏里拉': 'estimated_mozzarella',\n", "        '预估提升量_波波丨晶球': 'estimated_popping_boba',\n", "        '预估提升量_新鲜蛋类': 'estimated_fresh_eggs',\n", "        '预估提升量_鲜牛奶': 'estimated_fresh_milk',\n", "        '预估提升量_糯米粉': 'estimated_glutinous_rice_flour',\n", "        '预估提升量_冷冻熟蔬菜制品': 'estimated_frozen_cooked_vegetables',\n", "        '预估提升量_果糖': 'estimated_fructose',\n", "        '预估提升量_马斯卡彭': 'estimated_mascarpone',\n", "        '预估提升量_切达再制干酪': 'estimated_cheddar_processed_cheese',\n", "        '预估提升量_水果罐头': 'estimated_canned_fruits',\n", "        '预估提升量_水果类馅料': 'estimated_fruit_fillings',\n", "        '预估提升量_冷冻果肉': 'estimated_frozen_fruit_pulp',\n", "        '预估提升量_芋圆': 'estimated_taro_balls'\n", "    }\n", "\n", "    def escape_sql_value(value: Any) -> str:\n", "        \"\"\"Escape SQL values properly\"\"\"\n", "        if pd.isna(value) or value is None:\n", "            return 'NULL'\n", "        elif isinstance(value, str):\n", "            # Escape single quotes and backslashes\n", "            escaped = str(value).replace(\"'\", \"''\").replace(\"\\\\\", \"\\\\\\\\\")\n", "            return f\"'{escaped}'\"\n", "        elif isinstance(value, (int, float)):\n", "            if pd.isna(value):\n", "                return 'NULL'\n", "            return str(value)\n", "        else:\n", "            # Convert other types to string and escape\n", "            escaped = str(value).replace(\"'\", \"''\").replace(\"\\\\\", \"\\\\\\\\\")\n", "            return f\"'{escaped}'\"\n", "\n", "    def generate_insert_statement(batch_df: pd.DataFrame, columns: list) -> str:\n", "        \"\"\"Generate a single INSERT statement for a batch of data\"\"\"\n", "        values_list = []\n", "        \n", "        for _, row in batch_df.iterrows():\n", "            values = []\n", "            for col in columns:\n", "                chinese_col = next((k for k, v in column_mapping.items() if v == col), None)\n", "                if chinese_col and chinese_col in row:\n", "                    values.append(escape_sql_value(row[chinese_col]))\n", "                else:\n", "                    values.append('NULL')\n", "            values_list.append(f\"({', '.join(values)})\")\n", "        \n", "        columns_str = ', '.join(columns)\n", "        values_str = ',\\n    '.join(values_list)\n", "        \n", "        return f\"INSERT INTO {table_name} ({columns_str}) VALUES\\n    {values_str};\"\n", "\n", "    # Get the English column names in the order they appear in the table\n", "    english_columns = []\n", "    for chinese_col in df.columns:\n", "        if chinese_col in column_mapping:\n", "            english_columns.append(column_mapping[chinese_col])\n", "        else:\n", "            print(f\"Warning: Column '{chinese_col}' not found in mapping\")\n", "\n", "    # Write SQL file\n", "    with open(output_file, 'w', encoding='utf-8') as f:\n", "        f.write(f\"-- Generated SQL INSERT statements for {table_name}\\n\")\n", "        f.write(f\"-- Total records: {len(df)}\\n\")\n", "        f.write(f\"-- Batch size: {batch_size} records per INSERT statement\\n\")\n", "        f.write(f\"-- Generated at: {pd.Timestamp.now()}\\n\\n\")\n", "        f.write(\"SET NAMES utf8mb4;\\n\")\n", "        f.write(\"SET FOREIGN_KEY_CHECKS = 0;\\n\")\n", "        f.write(\"SET AUTOCOMMIT = 0;\\n\")\n", "        f.write(\"START TRANSACTION;\\n\\n\")\n", "        f.write(f\"DELETE FROM {table_name};\\n\\n\")\n", "        \n", "        # Process data in batches\n", "        total_records = len(df)\n", "        for i in range(0, total_records, batch_size):\n", "            batch_df = df.iloc[i:i+batch_size]\n", "            insert_statement = generate_insert_statement(batch_df, english_columns)\n", "            f.write(insert_statement)\n", "            f.write(\"\\n\\n\")\n", "            \n", "            # Progress indicator\n", "            processed = min(i + batch_size, total_records)\n", "            print(f\"Generated batch {(i//batch_size)+1}: {processed}/{total_records} records ({len(batch_df)} rows in this batch)\")\n", "        \n", "        f.write(\"COMMIT;\\n\")\n", "        f.write(\"SET FOREIGN_KEY_CHECKS = 1;\\n\")\n", "        f.write(\"SET AUTOCOMMIT = 1;\\n\")\n", "        f.write(\"-- End of generated SQL\\n\")\n", "\n", "    print(f\"SQL file generated successfully: {output_file}\")\n", "    print(f\"Total records processed: {len(df)}\")\n", "    print(f\"Number of INSERT statements: {(len(df) + batch_size - 1) // batch_size}\")\n", "    print(f\"Records per statement: {batch_size}\")\n", "    print(f\"Columns mapped: {len(english_columns)}\")\n", "    print(f\"Performance optimization: Using batch INSERT with {batch_size} rows per statement\")\n", "\n", "\n", "# Generate SQL file\n", "generate_sql_insert_file(cust_df, 'customer_usage_insert.sql', 'customer_usage_data', batch_size=1000)\n", "\n", "print(\"\\nSample DataFrame columns:\")\n", "print(cust_df.columns.tolist())\n", "print(f\"\\nSample DataFrame shape: {cust_df.shape}\")\n", "print(\"\\nFirst few rows:\")\n", "print(cust_df.head(3))"]}, {"cell_type": "code", "execution_count": null, "id": "f366300c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 5}