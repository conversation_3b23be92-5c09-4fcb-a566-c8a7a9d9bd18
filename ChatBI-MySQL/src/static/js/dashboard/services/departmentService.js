/**
 * Department Service
 *
 * 提供部门数据的API服务
 * 集中管理所有部门相关的API调用，实现更好的关注点分离
 */

/**
 * 获取所有一级部门列表
 * @returns {Promise<Array>} 部门名称列表
 */
export const fetchDepartments = async () => {
    try {
        const response = await fetch('/api/dashboard/departments');
        
        if (!response.ok) {
            throw new Error(`获取部门列表失败: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        return data.departments || [];
    } catch (error) {
        console.error('获取部门列表时发生错误:', error);
        throw error;
    }
};

/**
 * 带重试机制的获取部门列表
 * @param {number} maxRetries 最大重试次数，默认为3
 * @param {number} retryDelay 重试延迟时间（毫秒），默认为1000
 * @returns {Promise<Array>} 部门名称列表
 */
export const fetchDepartmentsWithRetry = async (maxRetries = 3, retryDelay = 1000) => {
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            return await fetchDepartments();
        } catch (error) {
            lastError = error;
            console.warn(`获取部门列表失败，第 ${attempt} 次尝试:`, error.message);
            
            // 如果不是最后一次尝试，则等待后重试
            if (attempt < maxRetries) {
                await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
        }
    }
    
    // 所有重试都失败了，抛出最后一个错误
    throw lastError;
};
