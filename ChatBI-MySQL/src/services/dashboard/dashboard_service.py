"""
Dashboard service module.

This module provides business logic for dashboard-related operations,
including authentication, statistics, and data retrieval for the dashboard.
"""

from functools import wraps
from typing import List, Dict, Any, Optional

from flask import session, request, redirect, url_for

from src.repositories.chatbi.dashboard.history import (
    query_conversations_for_dashboard,
    get_filtered_conversation_count_for_dashboard
)
from src.repositories.chatbi.dashboard.statistics import get_dashboard_stats, get_daily_usage_data, get_daily_users_data
from src.repositories.chatbi.dashboard.users import (
    get_unique_users, get_conversation_count_by_user,
    get_top_users, get_top_agents, get_departments
)
from src.utils.logger import logger
from src.db.connection import execute_db_query


# --- Dashboard Service Functions ---

def get_daily_users_statistics(days: int = 30) -> Dict[str, list]:
    """
    Get daily unique user count for the specified number of days.
    Args:
        days (int, optional): The number of days to get data for. Defaults to 30.
    Returns:
        Dict[str, List]: A dictionary containing dates and user counts
    """
    logger.info(f"Dashboard service: Requesting daily users data for the past {days} days")
    return get_daily_users_data(days)


def get_conversations_for_dashboard(filters: Optional[Dict[str, Any]] = None,
                                   limit: int = 20,
                                   offset: int = 0) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get filtered chatbot conversations for the dashboard, grouped by conversation_id.

    Args:
        filters (Dict[str, Any], optional): Filters to apply to the query. Defaults to None.
        limit (int, optional): Maximum number of conversations to return. Defaults to 20.
        offset (int, optional): Number of conversations to skip. Defaults to 0.

    Returns:
        Dict[str, List[Dict[str, Any]]]: A dictionary of conversations, keyed by conversation_id
    """
    logger.info(f"Dashboard service: Requesting conversations with filters: {filters}, limit: {limit}, offset: {offset}")
    return query_conversations_for_dashboard(filters, limit, offset)


def get_filtered_conversation_count(filters: Optional[Dict[str, Any]] = None) -> int:
    """
    Get the count of conversations matching the specified filters.

    Args:
        filters (Dict[str, Any], optional): Filters to apply to the query. Defaults to None.

    Returns:
        int: The count of conversations matching the filters
    """
    logger.info(f"Dashboard service: Requesting conversation count with filters: {filters}")
    return get_filtered_conversation_count_for_dashboard(filters)


def get_unique_users_list() -> List[Dict[str, str]]:
    """
    Get a list of all unique users.

    Returns:
        List[Dict[str, str]]: A list of unique users
    """
    logger.info("Dashboard service: Requesting unique users list")
    return get_unique_users()


def get_conversation_count_by_user_list() -> Dict[str, int]:
    """
    Get the count of unique conversations for each user.

    Returns:
        Dict[str, int]: A dictionary mapping emails to conversation counts
    """
    logger.info("Dashboard service: Requesting conversation counts by user")
    return get_conversation_count_by_user()


def get_dashboard_statistics(start_time: Optional[int] = None, end_time: Optional[int] = None,
                           filter_admin: bool = False) -> Dict[str, int]:
    """
    Get dashboard statistics for a specified time range.

    Args:
        start_time (int, optional): Start timestamp in milliseconds. Defaults to None.
        end_time (int, optional): End timestamp in milliseconds. Defaults to None.
        filter_admin (bool, optional): Whether to filter out admin users. Defaults to False.

    Returns:
        Dict[str, int]: A dictionary containing statistics
    """
    logger.info(f"Dashboard service: Requesting dashboard statistics with time range [{start_time}, {end_time}] and filter_admin={filter_admin}")
    return get_dashboard_stats(start_time, end_time, filter_admin)


def get_daily_usage_statistics(days: int = 30) -> Dict[str, List]:
    """
    Get daily usage data for the specified number of days.

    Args:
        days (int, optional): The number of days to get data for. Defaults to 30.

    Returns:
        Dict[str, List]: A dictionary containing dates, query counts, and bad case conversation counts
    """
    logger.info(f"Dashboard service: Requesting daily usage data for the past {days} days")
    return get_daily_usage_data(days)


def get_top_users_statistics(limit: int = 10, start_time: Optional[int] = None, end_time: Optional[int] = None,
                           filter_admin: bool = False) -> List[Dict[str, Any]]:
    """
    Get statistics for top users with the most queries within a specified time range.

    Args:
        limit (int, optional): Maximum number of users to return. Defaults to 10.
        start_time (int, optional): Start timestamp in milliseconds. Defaults to None.
        end_time (int, optional): End timestamp in milliseconds. Defaults to None.
        filter_admin (bool, optional): Whether to filter out admin users. Defaults to False.

    Returns:
        List[Dict[str, Any]]: A list of top users with their query and conversation counts
    """
    logger.info(f"Dashboard service: Requesting top {limit} users with time range [{start_time}, {end_time}] and filter_admin={filter_admin}")
    return get_top_users(limit, start_time, end_time, filter_admin)


def get_top_agents_statistics(limit: int = 10, start_time: Optional[int] = None, end_time: Optional[int] = None,
                            filter_admin: bool = False) -> List[Dict[str, Any]]:
    """
    Get statistics for top agents with the most conversations within a specified time range.

    Args:
        limit (int, optional): Maximum number of agents to return. Defaults to 10.
        start_time (int, optional): Start timestamp in milliseconds. Defaults to None.
        end_time (int, optional): End timestamp in milliseconds. Defaults to None.
        filter_admin (bool, optional): Whether to filter out admin users. Defaults to False.

    Returns:
        List[Dict[str, Any]]: A list of top agents with their conversation counts
    """
    logger.info(f"Dashboard service: Requesting top {limit} agents with time range [{start_time}, {end_time}] and filter_admin={filter_admin}")
    return get_top_agents(limit, start_time, end_time, filter_admin)


def get_departments_list() -> List[str]:
    """
    Get a list of all unique first-level departments.
    
    This method provides the business logic layer for retrieving department data,
    calling the repository layer and handling any business-specific processing.

    Returns:
        List[str]: A list of unique department names sorted alphabetically
    """
    logger.info("Dashboard service: Requesting departments list")
    try:
        departments = get_departments()
        logger.info(f"Dashboard service: Successfully retrieved {len(departments)} departments")
        return departments
    except Exception as e:
        logger.error(f"Dashboard service: Error retrieving departments list: {e}", exc_info=True)
        # Return empty list on error to maintain API consistency
        return []


# --- Dashboard Authentication ---

def admin_required(f):
    """
    装饰器：检查用户是否已登录且具有管理员权限。
    如果没有权限，重定向到登录页面或返回403错误。
    """

    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 首先检查用户是否通过飞书登录
        if "user_info" not in session:
            logger.warning("管理员访问被拒绝：用户未通过飞书登录")
            # 重定向到飞书登录，传递预期的dashboard URL
            return redirect(url_for('login_route', next=request.url))

        user_info = session.get("user_info")
        union_id = user_info.get("union_id")  # 使用飞书用户的union_id
        username = user_info.get("name")

        if not union_id:
            logger.warning(f"管理员访问被拒绝：无法获取用户union_id，用户名: {username}")
            return "无法验证用户身份", 403

        # 从数据库查询用户的is_admin字段
        try:
            sql = "SELECT is_admin FROM user WHERE union_id = %s"
            result = execute_db_query(sql, (union_id,), fetch='one')
            
            if not result:
                logger.warning(f"管理员访问被拒绝：用户不存在于数据库中，union_id: {union_id}, 用户名: {username}")
                return "用户不存在", 403
            
            # 检查is_admin字段
            is_admin = result[0] if isinstance(result, tuple) else result.get('is_admin', 0)
            
            if not is_admin:
                logger.warning(f"管理员访问被拒绝：用户 {username} (union_id: {union_id}) 不是管理员")
                return "您没有访问dashboard的权限", 403
                
            logger.info(f"管理员权限验证通过：用户 {username} (union_id: {union_id})")
            
        except Exception as e:
            logger.error(f"检查管理员权限时发生错误：{e}", exc_info=True)
            return "权限验证失败", 500

        # 如果所有检查都通过，继续执行被装饰的函数
        return f(*args, **kwargs)

    return decorated_function
