"""
Agent的模型提供者配置
支持多provider配置，包括鲜沐内部API和OpenRouter等
"""
import os
from typing import Dict, Any, Optional
from agents import set_tracing_disabled
from .cache_enabled_litellm_model import CacheEnabledLitellmModel
from src.utils.logger import logger

# 提供者配置
PROVIDERS_CONFIG = {
    "xm": {
        "api_key_env": "PROVIDER_XM_API_KEY",
        "api_base_env": "PROVIDER_XM_API_BASE",
        "default_model": os.getenv("PROVIDER_XM_DEFAULT_MODEL", "deepseek-v3-250324"),
        "fast_model": os.getenv("PROVIDER_XM_FAST_MODEL", "deepseek-v3-250324"),
    },
    "openrouter": {
        "api_key_env": "PROVIDER_OPENROUTER_API_KEY", 
        "api_base_env": "PROVIDER_OPENROUTER_API_BASE",
        "default_model": os.getenv("PROVIDER_OPENROUTER_DEFAULT_MODEL", "google/gemini-2.5-pro"),
        "fast_model": os.getenv("PROVIDER_OPENROUTER_FAST_MODEL", "openai/gpt-4.1-mini"),
        "claude_model": os.getenv("PROVIDER_OPENROUTER_CLAUDE_MODEL", "anthropic/claude-sonnet-4"),
    }
}

# 默认提供者
DEFAULT_PROVIDER = os.getenv("DEFAULT_MODEL_PROVIDER", "openrouter")

set_tracing_disabled(disabled=False)

def get_provider_config(provider: str) -> Dict[str, Any]:
    """
    获取指定提供者的配置信息
    
    Args:
        provider: 提供者名称 (xm, openrouter)
        
    Returns:
        Dict[str, Any]: 提供者配置信息
        
    Raises:
        ValueError: 当提供者不存在或配置不完整时
    """
    if provider not in PROVIDERS_CONFIG:
        raise ValueError(f"不支持的提供者: {provider}，支持的提供者: {list(PROVIDERS_CONFIG.keys())}")
    
    config = PROVIDERS_CONFIG[provider].copy()
    
    # 检查API密钥是否配置
    api_key = os.getenv(config["api_key_env"])
    if not api_key:
        # 如果是开发环境，提供更详细的错误信息
        env_var = config["api_key_env"]
        logger.warning(f"提供者 {provider} 的API密钥未配置 (环境变量: {env_var})")
        
        # 在开发环境中，可以使用默认配置继续运行
        if os.getenv("ENVIRONMENT") == "development":
            logger.info(f"开发环境下使用默认配置继续运行")
            api_key = "dummy-key-for-development"
        else:
            raise ValueError(f"提供者 {provider} 的API密钥未配置，请设置环境变量 {env_var}")
    
    config["api_key"] = api_key
    
    # 获取可选的API基础URL
    api_base = os.getenv(config.get("api_base_env", ""))
    if api_base:
        config["api_base"] = api_base
    
    return config

def get_model_for_name(model_name: str, provider: Optional[str] = None) -> CacheEnabledLitellmModel:
    """
    根据模型名称和提供者创建模型实例
    
    Args:
        model_name: 模型名称
        provider: 提供者名称，如果为None则使用默认提供者
        
    Returns:
        CacheEnabledLitellmModel: 配置好的模型实例
        
    Raises:
        ValueError: 当提供者不存在或配置错误时抛出异常
    """
    if provider is None:
        provider = DEFAULT_PROVIDER
    
    config = get_provider_config(provider)
    
    # 确保模型名称包含openai/前缀（litellm要求）
    if not model_name.startswith("openai/"):
        model_name = f"openai/{model_name}"
    
    # 构建模型参数
    model_kwargs = {
        "model": model_name,
        "api_key": config["api_key"],
    }
    
    # 只有当api_base存在时才添加
    if config.get("api_base"):
        model_kwargs["base_url"] = config["api_base"]
    
    return CacheEnabledLitellmModel(**model_kwargs)

def get_default_model(provider: Optional[str] = None) -> CacheEnabledLitellmModel:
    """
    获取指定提供者的默认模型
    
    Args:
        provider: 提供者名称，如果为None则使用默认提供者
        
    Returns:
        CacheEnabledLitellmModel: 默认模型实例
    """
    if provider is None:
        provider = DEFAULT_PROVIDER
    
    config = get_provider_config(provider)
    return get_model_for_name(config["default_model"], provider)

def get_fast_model(provider: Optional[str] = None) -> CacheEnabledLitellmModel:
    """
    获取指定提供者的快速模型
    
    Args:
        provider: 提供者名称，如果为None则使用默认提供者
        
    Returns:
        CacheEnabledLitellmModel: 快速模型实例
    """
    if provider is None:
        provider = DEFAULT_PROVIDER
    
    config = get_provider_config(provider)
    return get_model_for_name(config["fast_model"], provider)

def get_claude_model(provider: Optional[str] = None) -> CacheEnabledLitellmModel:
    """
    获取Claude模型（仅openrouter提供者支持）
    
    Args:
        provider: 提供者名称，如果为None则使用openrouter
        
    Returns:
        CacheEnabledLitellmModel: Claude模型实例
    """
    if provider is None:
        provider = "openrouter"
    
    config = get_provider_config(provider)
    claude_model = config.get("claude_model")
    if not claude_model:
        raise ValueError(f"提供者 {provider} 不支持Claude模型")
    
    return get_model_for_name(claude_model, provider)
