"""
Event formatting utilities for handling agent output events.
"""
import json
import re
from src.utils.logger import logger
from agents import ItemHelpers


def _get_known_agents() -> list:
    """
    动态获取当前配置的所有可用agent名称
    
    Returns:
        list: 包含所有可用agent名称的列表
    """
    try:
        from src.services.agent.agent_service import get_agent_names
        
        # 直接获取配置文件中的agent名称，不做任何修改
        all_agents = get_agent_names()
        logger.debug(f"动态获取的agent名称列表: {all_agents}")
        
        # 直接返回原始名称列表，去重
        return list(set(all_agents))
        
    except Exception as e:
        logger.warning(f"动态获取agent列表失败，使用默认列表: {e}")
        # 如果动态获取失败，返回默认列表
        return [
            'sales_order_analytics', 'sales_kpi_analytics', 
            'warehouse_and_fulfillment', 'general_chat_bot',
            'coordinator_bot', 'catering_expert', 'sale_bd_manage_merchat'
        ]


def _extract_agent_name_from_tool_call(tool_call_item) -> str:
    """
    从工具调用项中提取agent名称
    
    Args:
        tool_call_item: 工具调用项对象
        
    Returns:
        str: 提取的agent名称，如果无法提取则返回None
    """
    try:
        # 动态获取已知的agent名称列表
        known_agents = _get_known_agents()
        
        # 尝试从raw_item中获取name属性
        if hasattr(tool_call_item, 'raw_item') and tool_call_item.raw_item:
            raw_item = tool_call_item.raw_item
            if hasattr(raw_item, 'name') and raw_item.name:
                agent_name = raw_item.name
                # 检查是否是已知的agent名称
                if agent_name in known_agents:
                    return agent_name
        
        # 尝试从字符串表示中提取name
        tool_call_str = str(tool_call_item.raw_item if hasattr(tool_call_item, 'raw_item') else tool_call_item)
        
        # 使用正则表达式匹配name='agent_name'模式
        name_match = re.search(r"name='([^']+)'", tool_call_str)
        if name_match:
            agent_name = name_match.group(1)
            # 检查是否是已知的agent名称
            if agent_name in known_agents:
                return agent_name
        
        logger.debug(f"无法从工具调用中提取agent名称: {tool_call_str}")
        return None
        
    except Exception as e:
        logger.error(f"提取agent名称时出错: {e}")
        return None


def format_event_message(event) -> dict:
    """
    Format events into readable message strings and return as JSON objects.

    Args:
        event: The event object to format

    Returns:
        dict: A formatted message dictionary
    """
    try:
        if (
                event.type == "raw_response_event"
                and hasattr(event.data, "delta")
                and event.data.delta
        ):
            return _handle_raw_response_event(event)
        elif event.type == "agent_thinking_event":
            return _handle_agent_thinking_event(event)
        elif event.type == "run_item_stream_event":
            return _handle_run_item_stream_event(event)
        else:
            # logger.warning(f"Unknown event: {event}")
            pass
    except Exception as e:
        logger.exception(f"Error formatting event: {e}, event: {event}")
        return {"type": "log", "content": str(e), "error": True}


def _handle_raw_response_event(event) -> dict:
    """
    Handle raw response events, typically containing JSON data.

    Args:
        event: The raw response event

    Returns:
        dict: A formatted message dictionary
    """
    try:
        parsed = json.loads(event.data.delta)
        if isinstance(parsed, dict):
            # 这三个字段是工具调用的参数，如果存在，则返回，以告知用户AI正在使用工具
            tool_call_args = parsed.get("input", parsed.get("description", parsed.get("product_name")))
            if tool_call_args:
                return {"type": "data", "content": f"\n{tool_call_args}\n___\n"}
            else:
                return {
                    "type": "log",
                    "content": "".join(
                        [f"- {key}: {value}\n" for key, value in parsed.items()]) + "\n___\n",
                }
        else:
            return {"type": "data", "content": str(parsed)}
    except (json.JSONDecodeError, TypeError):
        return {"type": "data", "content": event.data.delta}


def _handle_agent_thinking_event(event) -> dict:
    """
    Handle agent thinking events.

    Args:
        event: The agent thinking event

    Returns:
        dict: A formatted message dictionary
    """
    return {"type": "thinking", "content": event.data.thinking}


def _handle_run_item_stream_event(event) -> dict:
    """
    Handle run item stream events.

    Args:
        event: The run item stream event

    Returns:
        dict: A formatted message dictionary
    """
    if event.item.type == "tool_call_item":
        # 提取agent名称并生成handoff_log
        agent_name = _extract_agent_name_from_tool_call(event.item)
        if agent_name:
            return {
                "type": "handoff_log",
                "content": agent_name  # 仅包含agent名称
            }
        else:
            return {
                "type": "tool_call_log",
                "content": f"{event.item.raw_item if hasattr(event.item, 'raw_item') else event.item}",
            }
    elif event.item.type == "tool_call_output_item":
        return _handle_tool_call_output_item(event)
    elif event.item.type == "handoff_output_item":
        # Handle handoff output items specifically
        # HandoffOutputItem doesn't have an output attribute, but we can access
        # the source and target agents
        source_agent = getattr(event.item, 'source_agent', None)
        target_agent = getattr(event.item, 'target_agent', None)

        if target_agent and hasattr(target_agent, 'name'):
            # 仅返回目标agent的名称
            content = target_agent.name
        else:
            content = "unknown_agent"

        return {
            "type": "handoff_log",
            "content": content  # 仅包含agent名称
        }
    elif event.item.type == "message_output_item":
        return {
            "type": "log",
            "content": ItemHelpers.text_message_output(event.item),
        }


def _handle_tool_call_output_item(event) -> dict:
    """
    Handle tool call output items.

    Args:
        event: The tool call output event

    Returns:
        dict: A formatted message dictionary
    """
    output = str(event.item.output)
    # Check if the output is likely DDL content (adjust as needed)
    is_ddl_read_operator = "CREATE TABLE" in output or "TABLE_SCHEMA" in output
    return {
        "type": "tool_output",
        "content": "AI读取了DDL文件" if is_ddl_read_operator else output,
    }
