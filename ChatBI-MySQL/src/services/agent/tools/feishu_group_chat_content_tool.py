"""
飞书群聊内容搜索相关工具。
"""

import os
import uuid
import aiohttp
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from agents import RunContextWrapper
from src.models.user_info_class import UserInfo
from src.utils.logger import logger
from src.services.agent.tools.tool_manager import tool_manager
from src.services.feishu.message_content import get_message_content
from src.services.feishu.chat_list_service import get_group_chat_ids,get_chat_info_by_id

# 消息内容限制
MESSAGE_CONTENT_LIMIT = int(os.getenv("MESSAGE_CONTENT_LIMIT", 1000))

# 并发控制：最多3个并发请求
CONCURRENT_LIMIT = 3

async def search_feishu_group_chat_content(
    wrapper: RunContextWrapper[UserInfo],
    query: str,
    page_size: int = 20,
    get_content: bool = True,
) -> Tuple[List[Dict[str, Any]], str]:
    """搜索飞书群聊消息并获取内容。

    该工具用于在飞书群聊中搜索相关消息，可以帮助AI获取群聊中的历史对话和信息。如果飞书文档中找不到所需要的知识，可以尝试使用本工具从群聊中获取。

    工作流程：
    1. 使用搜索接口根据关键词在群聊中查找相关消息ID
    2. 如果get_content为True，会进一步调用消息详情接口获取每条消息的具体内容

    Args:
        wrapper: 包含用户信息的上下文包装器。
        query: 搜索关键词，建议使用具体的业务术语或问题描述。
        page_size: 返回结果数量，默认为20，最大不超过50。
        get_content: 是否获取消息详细内容，默认为True。如果为True，会进一步调用详情接口获取每条消息的具体内容。

    Returns:
        Tuple[List[Dict[str, Any]], str]: 搜索结果列表和描述信息。
        搜索结果包含：message_id（消息ID）、content（消息内容，仅当get_content=True时）、sender（发送者信息）、create_time（创建时间）
    """
    user_info = wrapper.context
    access_token = user_info.access_token
    
    if not access_token:
        error_msg = "用户未提供飞书访问令牌"
        logger.warning(error_msg)
        return [], f"搜索飞书群聊消息失败: {error_msg}"
    
    search_id = str(uuid.uuid4())
    logger.info(f"用户开始搜索飞书群聊消息，搜索ID: {search_id}, 关键词: {query}, access_token: {access_token[:20]}...")
    
    try:
        chat_ids = get_group_chat_ids()
        if chat_ids:
            logger.info(f"自动获取到 {len(chat_ids)} 个群聊ID，将在这些群聊中搜索消息")

        # 调用搜索接口
        search_url = f"https://open.feishu.cn/open-apis/search/v2/message?page_size={page_size}&user_id_type=union_id"
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {access_token}'
        }
        search_data = {
            'chat_type': 'group_chat',
            'from_type': 'user',
            'query': query,
            'chat_ids': chat_ids if chat_ids and len(chat_ids) > 0 else None
        }
        
        # 设置超时时间为30秒，避免长时间等待
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(search_url, headers=headers, json=search_data) as response:
                if response.status != 200:
                    error_msg = f"搜索接口调用失败，状态码: {response.status}, 响应内容: {await response.text()}"
                    logger.exception(error_msg)
                    return [], f"搜索飞书群聊消息失败: {error_msg}"
                
                search_result = await response.json()
                
                if search_result.get('code') != 0:
                    error_msg = f"搜索接口返回错误: {search_result.get('msg', '未知错误')}"
                    logger.exception(error_msg)
                    return [], f"搜索飞书群聊消息失败: {error_msg}"
                
                message_ids = search_result.get('data', {}).get('items', [])
                
                if not message_ids:
                    return [], f"未找到与关键词 '{query}' 相关的群聊消息"
                
                logger.info(f"搜索到 {len(message_ids)} 条消息ID")
                
                # 如果不需要获取内容，直接返回消息ID列表
                if not get_content:
                    result_items = [{'message_id': msg_id} for msg_id in message_ids]
                    return result_items, f"成功搜索到 {len(message_ids)} 条相关消息ID"
                
                # 并发获取每条消息的详细内容
                enriched_items, failed_count = await _get_messages_content_concurrently(message_ids)

                success_msg = f"成功搜索到 {len(message_ids)} 条消息"
                if failed_count > 0:
                    success_msg += f"，其中 {failed_count} 条消息内容获取失败（可能缺少权限）"

                return enriched_items, success_msg
                
    except Exception as e:
        error_msg = f"搜索飞书群聊消息时发生异常: {str(e)}"
        logger.exception(error_msg)
        return [], f"搜索飞书群聊消息失败: {error_msg}"


async def get_feishu_message_content_tool(
    wrapper: RunContextWrapper[UserInfo], 
    message_id: str
) -> Tuple[Optional[Dict[str, Any]], str]:
    """获取指定飞书消息的详细内容。

    Args:
        wrapper: 包含用户信息的上下文包装器。
        message_id: 消息的ID标识符。

    Returns:
        Tuple[Optional[Dict[str, Any]], str]: 消息内容和描述信息。
    """
    user_info = wrapper.context
    access_token = user_info.access_token
    
    if not access_token:
        error_msg = "用户未提供飞书访问令牌"
        logger.warning(error_msg)
        return None, f"获取消息内容失败: {error_msg}"
    
    logger.info(f"开始获取飞书消息内容，message_id: {message_id}")

    message_content = await _get_feishu_message_content(message_id)
    
    if message_content:
        return message_content, f"成功获取消息内容"
    else:
        return None, f"获取消息内容失败，message_id: {message_id}"


async def _get_messages_content_concurrently(message_ids: List[str]) -> Tuple[List[Dict[str, Any]], int]:
    """并发获取多条消息的内容。

    Args:
        message_ids: 消息ID列表。

    Returns:
        Tuple[List[Dict[str, Any]], int]: 消息内容列表和失败数量。
    """
    # 创建信号量来控制并发数量
    semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)

    async def get_single_message_content(message_id: str) -> Dict[str, Any]:
        """获取单条消息内容的包装函数"""
        async with semaphore:
            message_content = await _get_feishu_message_content(message_id)
            if message_content:
                return message_content
            else:
                # 如果获取内容失败，至少保留消息ID
                return {'message_id': message_id, 'content': '获取内容失败（可能缺少权限）'}

    # 创建并发任务
    tasks = [get_single_message_content(message_id) for message_id in message_ids]

    # 并发执行所有任务
    logger.info(f"开始并发获取 {len(message_ids)} 条消息的内容，并发数: {CONCURRENT_LIMIT}")
    enriched_items = await asyncio.gather(*tasks, return_exceptions=True)

    # 处理异常结果和统计失败数量
    result_items = []
    failed_count = 0
    for i, result in enumerate(enriched_items):
        if isinstance(result, Exception):
            logger.exception(f"获取第 {i+1} 条消息内容时发生异常: {result}")
            result_items.append({
                'message_id': message_ids[i],
                'content': f'获取内容失败: {str(result)}'
            })
            failed_count += 1
        else:
            if "image_url" in result:
                result["image_url"] = "(图片已隐藏)"
            result_items.append(result)
            # 检查是否是获取失败的情况
            if result.get('content') == '获取内容失败（可能缺少权限）':
                failed_count += 1

    logger.info(f"并发获取消息内容完成，成功: {len(message_ids) - failed_count}/{len(message_ids)}")
    return result_items, failed_count


async def _get_feishu_message_content(message_id: str) -> Optional[Dict[str, Any]]:
    """内部函数：获取飞书消息的详细内容。

    使用项目中已有的 get_message_content 函数，支持所有飞书消息类型。

    Args:
        message_id: 消息的ID标识符。

    Returns:
        Optional[Dict[str, Any]]: 消息的详细内容，获取失败时返回None。
    """
    try:
        logger.debug(f"正在获取飞书消息内容，message_id: {message_id}")

        # 使用项目中已有的消息内容获取函数
        message_content = get_message_content(message_id)

        if not message_content:
            logger.warning(f"无法获取消息内容，message_id: {message_id}")
            return None

        # 构建返回结果
        chat_info = get_chat_info_by_id(message_content.chat_id)
        result = {
            'message_id': message_id,
            'chat_id': message_content.chat_id,
            'chat_name': chat_info.get('name', '') if chat_info else '未知群聊',
            'content': message_content.text,
            'image_url': message_content.image_url,
        }

        # 处理消息内容长度限制
        content = result.get('content', '')
        if isinstance(content, str) and len(content) > MESSAGE_CONTENT_LIMIT:
            original_length = len(content)
            result['content'] = content[:MESSAGE_CONTENT_LIMIT] + f"\n\n(内容被截断，原长度: {original_length})"

        logger.debug(f"获取到消息内容，message_id: {message_id}, 内容长度: {len(content)} 字符, 是否有图片: {bool(message_content.image_url)}")
        return result

    except Exception as e:
        logger.exception(f"获取消息内容时发生异常: {str(e)}, message_id: {message_id}")
        return None


# 注册工具
tool_manager.register_as_function_tool(search_feishu_group_chat_content)
tool_manager.register_as_function_tool(get_feishu_message_content_tool)
