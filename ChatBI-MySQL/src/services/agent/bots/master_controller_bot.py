"""
Master controller bot implementation.

This bot acts as a central controller that can:
1. List all available data fetcher agents
2. Determine which specialized agent to use based on user input
3. Hand off tasks to specialized agents
4. Consolidate responses from specialized agents
"""
import textwrap
from typing import Optional, Dict, Any, List, Callable

from agents import Agent, handoff, RunContextWrapper, Model
from agents.extensions.handoff_prompt import prompt_with_handoff_instructions

from src.models.user_info_class import UserInfo
from src.services.agent.bots.base_bot import BaseBot
from src.services.agent.bots.data_fetcher_bot import DataFetcherBot
from src.services.agent.utils.model_provider import get_default_model
from src.utils.logger import logger
from src.utils.resource_manager import load_resource, list_resources

class MasterControllerBot(BaseBot):
    """
    Master controller bot that orchestrates multiple specialized agents.

    This bot is responsible for:
    - Listing all available data fetcher agents
    - Determining which specialized agent to use based on user input
    - Handing off tasks to specialized agents
    - Consolidating responses from specialized agents
    """

    def __init__(self, user_info: Dict[str, Any]):
        super().__init__(user_info)
        self.available_bots = self._get_available_bots()

    def _get_available_bots(self) -> List[BaseBot]:
        """
        Bot列表. 未来加入新bot类型时, 只需要更新这里.

        Returns:
            List[BaseBot]: List of available bots
        """
        bots = []

        # data_fetch的配置
        data_fetcher_config = list_resources('data_fetcher_bot_config', '.yml')

        for config in data_fetcher_config:
            bots.append(DataFetcherBot(self.user_info, config))

        return bots

    def get_description(self) -> str:
        return "我是一个主控制器，负责将用户的问题分配给最合适的专家助手。我会分析您的问题内容，自动选择最合适的专家来回答您的问题。"

    def create_agent(self, model: Optional[Model] = None) -> Agent:
        if model is None:
            model = get_default_model()
            
        instruction = load_resource("prompt", "master_controller_instruction.md")
        instruction = prompt_with_handoff_instructions(instruction)

        logger.info(f"Master controller bot instruction:{instruction[:50]}")


        # 创建子代理
        bot_list = self._get_available_bots()
        logger.info(f"Master bot using model: {model}")
        handoffs = []
        for bot in bot_list:
            sub_agent = bot.create_agent()
            sub_agent.handoff_description = bot.get_description()

            def create_handoff_logger(agent_name: str):
                def on_hand_off_log_name(context: RunContextWrapper[UserInfo]) -> None:
                    logger.info(f"🔄 MasterController执行handoff到: {agent_name}")
                    return None
                return on_hand_off_log_name

            handoffs.append(handoff(
                sub_agent,
                on_handoff=create_handoff_logger(sub_agent.name)
            ))

        # Create and return the master controller agent
        agent = Agent[UserInfo](
            name="主控制器",
            instructions=instruction,
            model=model,  # 使用传入的模型
            handoffs=handoffs
        )

        return agent
