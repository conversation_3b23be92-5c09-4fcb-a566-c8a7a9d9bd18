"""
Dashboard users repository module.

This module provides data access functions for user-related dashboard operations.
"""

from typing import List, Dict, Any, Optional
from mysql.connector import Error

from src.utils.logger import logger
from src.db.connection import execute_db_query

def get_unique_users() -> List[Dict[str, str]]:
    """
    Get all unique users (grouped by email and username) from MySQL.
    Returns a list in the format [{'username': '...', 'email': '...'}, ...].

    Returns:
        List[Dict[str, str]]: A list of unique users
    """
    # Select both username and email, and group by both to handle users with the same name but different emails
    sql = "SELECT DISTINCT username, email FROM chat_history ORDER BY username ASC, email ASC"
    users = []

    try:
        results = execute_db_query(sql, fetch='all')
        if results:
            users = results  # Results are already in the required dictionary list format
        logger.info(f"Got {len(users)} unique user combinations (username/email)")
        return users
    except Error as e:
        # Error already logged
        return []
    except Exception as e:
        logger.error(f"Unexpected error getting unique user list: {e}", exc_info=True)
        return []

def get_conversation_count_by_user() -> Dict[str, int]:
    """
    Get the count of unique conversations for each user (identified by email) from MySQL.
    Returns a dictionary in the format {email: count, ...}.

    Returns:
        Dict[str, int]: A dictionary mapping emails to conversation counts
    """
    # Group by email and count distinct conversations
    sql = """
        SELECT email, COUNT(DISTINCT conversation_id) as count
        FROM chat_history
        GROUP BY email
        ORDER BY email ASC
    """
    user_counts = {}

    try:
        results = execute_db_query(sql, fetch='all')
        if results:
            for row in results:
                user_counts[row['email']] = row['count']
        logger.info(f"Got conversation counts for {len(user_counts)} users")
        return user_counts
    except Error as e:
        # Error already logged
        return {}
    except Exception as e:
        logger.error(f"Unexpected error getting user conversation counts: {e}", exc_info=True)
        return {}


def get_top_users(limit: int = 10, start_time: Optional[int] = None, end_time: Optional[int] = None,
                 filter_admin: bool = False) -> List[Dict[str, Any]]:
    """
    Get the top users with the most queries within a specified time range.

    Args:
        limit (int, optional): Maximum number of users to return. Defaults to 10.
        start_time (int, optional): Start timestamp in milliseconds. Defaults to None.
        end_time (int, optional): End timestamp in milliseconds. Defaults to None.
        filter_admin (bool, optional): Whether to filter out admin users. Defaults to False.

    Returns:
        List[Dict[str, Any]]: A list of top users with their query and conversation counts
    """
    sql = """
        SELECT
            ch.username,
            ch.email,
            COUNT(CASE WHEN ch.role = 'user' THEN 1 ELSE NULL END) AS query_count,
            COUNT(DISTINCT ch.conversation_id) AS conversation_count
        FROM chat_history ch
    """
    
    # 如果需要过滤管理员，则需要关联user表
    if filter_admin:
        sql += """
        LEFT JOIN user u ON ch.email = u.email
        WHERE (u.is_admin IS NULL OR u.is_admin = 0)
        """
    else:
        sql += " WHERE 1=1"
    
    params = []

    # Add time range filters if provided
    if start_time is not None:
        sql += " AND ch.timestamp >= %s"
        params.append(start_time)
    if end_time is not None:
        sql += " AND ch.timestamp <= %s"
        params.append(end_time)

    # Group by user and order by query count
    sql += """
        GROUP BY ch.username, ch.email
        ORDER BY query_count DESC
        LIMIT %s
    """
    params.append(limit)

    try:
        results = execute_db_query(sql, tuple(params), fetch='all')
        if results:
            logger.info(f"Got top {len(results)} users by query count")
            return results
        else:
            logger.warning("No users found for top users query")
            return []
    except Error as e:
        # Error already logged
        return []
    except Exception as e:
        logger.error(f"Unexpected error getting top users: {e}", exc_info=True)
        return []



def get_top_agents(limit: int = 10, start_time: Optional[int] = None, end_time: Optional[int] = None,
                  filter_admin: bool = False) -> List[Dict[str, Any]]:
    """
    获取指定时间范围内使用量最高的agents统计。
    能够正确处理复合agent名称（如"sales_order_analytics,warehouse_and_fulfillment"），
    将每个agent分别计算使用量。
    只统计role=assistant的记录，因为这些记录才是真实的agent使用记录。

    Args:
        limit (int, optional): 返回的最大agent数量. Defaults to 10.
        start_time (int, optional): 开始时间戳（毫秒）. Defaults to None.
        end_time (int, optional): 结束时间戳（毫秒）. Defaults to None.
        filter_admin (bool, optional): 是否过滤管理员用户. Defaults to False.

    Returns:
        List[Dict[str, Any]]: 按使用量排序的agent列表，每个包含agent_name和conversation_count
    """
    # 首先获取所有符合条件的对话记录，只统计role=assistant的记录
    sql = """
        SELECT DISTINCT ch.conversation_id, ch.agent
        FROM chat_history ch
    """
    
    # 如果需要过滤管理员，则需要关联user表
    if filter_admin:
        sql += " LEFT JOIN user u ON ch.email = u.email"
    
    # 添加基础过滤条件
    sql += """
        WHERE ch.agent IS NOT NULL 
        AND ch.agent != '' 
        AND ch.role = 'assistant'
    """
    
    # 如果需要过滤管理员，添加管理员过滤条件
    if filter_admin:
        sql += " AND (u.is_admin IS NULL OR u.is_admin = 0)"
    
    params = []

    # 添加时间范围过滤条件
    if start_time is not None:
        sql += " AND ch.timestamp >= %s"
        params.append(start_time)
    if end_time is not None:
        sql += " AND ch.timestamp <= %s"
        params.append(end_time)

    try:
        # 获取所有对话记录
        results = execute_db_query(sql, tuple(params), fetch='all')
        if not results:
            logger.warning("No conversations found for top agents query")
            return []

        # 统计每个agent的使用量
        agent_counts = {}
        
        for row in results:
            conversation_id = row['conversation_id']
            agent_string = row['agent']
            
            if not agent_string:
                continue
                
            # 处理复合agent名称，按逗号分割
            individual_agents = [agent.strip() for agent in agent_string.split(',') if agent.strip()]
            
            # 为每个单独的agent计数
            for agent in individual_agents:
                if agent not in agent_counts:
                    agent_counts[agent] = set()  # 使用set来避免重复计算同一个对话
                agent_counts[agent].add(conversation_id)
        
        # 转换为最终格式并排序
        agent_stats = []
        for agent_name, conversation_set in agent_counts.items():
            agent_stats.append({
                'agent_name': agent_name,
                'conversation_count': len(conversation_set)
            })
        
        # 按使用量降序排序并限制数量
        agent_stats.sort(key=lambda x: x['conversation_count'], reverse=True)
        result = agent_stats[:limit]
        
        logger.info(f"Got top {len(result)} agents by conversation count")
        return result
        
    except Error as e:
        # Error already logged
        return []
    except Exception as e:
        logger.error(f"Unexpected error getting top agents: {e}", exc_info=True)
        return []


def get_departments() -> List[str]:
    """
    Get all unique first-level departments from the user table.
    Returns a list of department names sorted alphabetically.

    Returns:
        List[str]: A list of unique department names
    """
    sql = """
        SELECT DISTINCT first_level_department 
        FROM user 
        WHERE first_level_department IS NOT NULL 
        AND first_level_department != ''
        ORDER BY first_level_department ASC
    """
    departments = []

    try:
        results = execute_db_query(sql, fetch='all')
        if results:
            # Extract department names from the result dictionaries
            departments = [row['first_level_department'] for row in results]
        logger.info(f"Got {len(departments)} unique departments")
        return departments
    except Error as e:
        # Error already logged by execute_db_query
        logger.error(f"Database error getting departments: {e}")
        return []
    except Exception as e:
        logger.error(f"Unexpected error getting departments: {e}", exc_info=True)
        return []
