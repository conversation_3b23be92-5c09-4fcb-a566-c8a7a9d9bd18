"""
Dashboard history repository module.

This module provides data access functions for chatbot history in the dashboard.
"""

from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from mysql.connector import Error

from src.utils.logger import logger
from src.db.connection import execute_db_query, get_db_connection




def get_filtered_conversation_count_for_dashboard(filters: Optional[Dict[str, Any]] = None) -> int:
    """
    Get the count of unique conversations matching the specified filters.

    Args:
        filters (Dict[str, Any], optional): Filters to apply to the query. Defaults to None.

    Returns:
        int: The count of conversations matching the filters
    """
    if filters is None:
        filters = {}

    # Check if we need to join with user table for department filtering
    needs_user_join = filters.get('first_level_department') or filters.get('filter_admin')
    
    base_query = """
        SELECT COUNT(DISTINCT ch.conversation_id) as count
        FROM chat_history ch
        LEFT JOIN bad_case bc ON ch.conversation_id = bc.conversation_id
        LEFT JOIN good_case gc ON ch.conversation_id = gc.conversation_id
    """
    
    # Add LEFT JOIN with user table if needed for department filtering
    if needs_user_join:
        base_query += """
        LEFT JOIN user u ON ch.email = u.email
        """
    
    where_clauses, params = _build_where_clauses_from_filters(filters)

    # Combine query
    query = base_query
    if where_clauses:
        query += " WHERE " + " AND ".join(where_clauses)

    try:
        result = execute_db_query(query, tuple(params), fetch='one')
        count = result.get('count', 0) if result else 0
        logger.info(f"Dashboard query returned {count} unique conversations")
        return count
    except Error as e:
        # Error already logged
        return 0
    except Exception as e:
        logger.error(f"Unexpected error counting dashboard conversations: {e}", exc_info=True)
        return 0


def query_conversations_for_dashboard(filters: Optional[Dict[str, Any]] = None,
                                     limit: int = 20,
                                     offset: int = 0) -> Dict[str, List[Dict[str, Any]]]:
    """
    Query chatbot conversations from MySQL for the dashboard, grouped by conversation_id.

    Args:
        filters (Dict[str, Any], optional): Filters to apply to the query. Defaults to None.
        limit (int, optional): Maximum number of conversations to return. Defaults to 20.
        offset (int, optional): Number of conversations to skip. Defaults to 0.

    Returns:
        Dict[str, List[Dict[str, Any]]]: A dictionary of conversations, keyed by conversation_id
    """
    if filters is None:
        filters = {}

    conn = None
    cursor = None
    conversations = {}

    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # Step 1: Get paginated conversation IDs
        where_clauses, params = _build_where_clauses_from_filters(filters)

        # Check if we need to join with user table for department filtering
        needs_user_join = filters.get('first_level_department') or filters.get('filter_admin')

        convo_sql = """
            SELECT ch.conversation_id, MAX(ch.timestamp) as last_message_time,
                   CASE WHEN bc.conversation_id IS NOT NULL THEN 1 ELSE 0 END as is_bad_case,
                   CASE WHEN gc.conversation_id IS NOT NULL THEN 1 ELSE 0 END as is_good_case,
                   bc.repair_status,
                   bc.repair_note,
                   MIN(ch.username) as username,
                   MIN(ch.email) as email,
                   MAX(ch.time_spend) as max_time_spend
        """
        
        # Add department information if user join is needed
        if needs_user_join:
            convo_sql += """,
                   MIN(u.first_level_department) as first_level_department
            FROM chat_history ch
            LEFT JOIN bad_case bc ON ch.conversation_id = bc.conversation_id
            LEFT JOIN good_case gc ON ch.conversation_id = gc.conversation_id
            LEFT JOIN user u ON ch.email = u.email
            """
        else:
            convo_sql += """
            FROM chat_history ch
            LEFT JOIN bad_case bc ON ch.conversation_id = bc.conversation_id
            LEFT JOIN good_case gc ON ch.conversation_id = gc.conversation_id
            """

        if where_clauses:
            convo_sql += " WHERE " + " AND ".join(where_clauses)

        convo_sql += """
            GROUP BY ch.conversation_id
            ORDER BY last_message_time DESC
            LIMIT %s OFFSET %s
        """

        query_params = params + [limit, offset]
        logger.debug(f"Executing conversation ID query: {convo_sql} | Params count: {len(query_params)}")
        cursor.execute(convo_sql, tuple(query_params))
        paginated_convos = cursor.fetchall()

        if not paginated_convos:
            logger.info(f"No conversations found with the specified filters (limit={limit}, offset={offset})")
            return {}

        # Extract conversation IDs and metadata
        paginated_convo_ids = []
        convo_metadata = {}

        for row in paginated_convos:
            convo_id = row['conversation_id']
            paginated_convo_ids.append(convo_id)

            # Store metadata for each conversation
            metadata = {
                'is_bad_case': bool(row['is_bad_case']),
                'is_good_case': bool(row['is_good_case']),
                'repair_status': row['repair_status'] if row['repair_status'] is not None else 0,
                'repair_note': row['repair_note'],
                'username': row['username'],
                'email': row['email'],
                'last_message_time': row['last_message_time'],
                'max_time_spend': row.get('max_time_spend')  # 添加该对话的最大time_spend
            }
            
            # Add department information if available
            if needs_user_join and 'first_level_department' in row:
                metadata['first_level_department'] = row['first_level_department']
            
            convo_metadata[convo_id] = metadata

        # Step 2: Get all messages for these conversations (including agent field and resource_url)
        placeholders = ','.join(['%s'] * len(paginated_convo_ids))
        messages_sql = f"""
            SELECT id, conversation_id, role, content, logs, timestamp,
                   username, email, output_as_input, agent, resource_url, time_spend
            FROM chat_history
            WHERE conversation_id IN ({placeholders})
            ORDER BY conversation_id, timestamp ASC
        """

        cursor.execute(messages_sql, tuple(paginated_convo_ids))
        rows = cursor.fetchall()

        # Step 3: Group messages by conversation ID and collect agents
        conversation_agents = {}  # Track agents per conversation
        for row in rows:
            convo_id = row['conversation_id']
            if convo_id not in conversations:
                conversations[convo_id] = []
                conversation_agents[convo_id] = set()

            # 处理agent信息（支持多agent字段）
            message_agents = []
            if row['role'] == 'assistant' and row.get('agent'):
                agent_str = row['agent'].strip()
                if agent_str and agent_str != '':
                    # 解析多agent字段（逗号分隔）
                    agents = [a.strip() for a in agent_str.split(',') if a.strip()]
                    for agent_name in agents:
                        # 清理agent名称（移除_specialist后缀）
                        clean_agent_name = agent_name.replace('_specialist', '')
                        conversation_agents[convo_id].add(clean_agent_name)
                        message_agents.append(clean_agent_name)

            # Format timestamp
            ts_ms = row.get('timestamp')
            formatted_time = None
            if ts_ms and isinstance(ts_ms, int):
                try:
                    dt_object = datetime.fromtimestamp(ts_ms / 1000)
                    formatted_time = dt_object.strftime('%Y-%m-%d %H:%M:%S')
                except (TypeError, ValueError, OSError):
                    formatted_time = f"Invalid timestamp: {ts_ms}"
            else:
                formatted_time = "No timestamp"

            # Truncate long content preview
            content = row.get('content', '')
            if not isinstance(content, str):
                content = str(content)
            content_preview = (content[:100] + '...') if len(content) > 100 else content

            # Add message to conversation
            # Note: is_bad_case is now determined at conversation level, not message level
            message = {
                'id': row['id'],
                'role': row['role'] or 'unknown',
                'content': content,
                'content_preview': content_preview,
                'logs': row['logs'],
                'timestamp': ts_ms,
                'formatted_time': formatted_time,
                'username': row['username'],
                'email': row['email'],
                'output_as_input': row['output_as_input'],
                'resource_url': row['resource_url'],  # 添加resource_url字段以支持图片显示
                'agents': message_agents,  # 添加消息级别的agent信息（数组格式）
                'time_spend': row.get('time_spend')  # 添加time_spend字段，仅对role=assistant的消息有意义
            }
            conversations[convo_id].append(message)

        # Add agents to conversation metadata
        for convo_id in conversation_agents:
            if convo_id in convo_metadata:
                agents_list = sorted(list(conversation_agents[convo_id]))  # Convert set to sorted list
                if agents_list:  # Only add if there are actual agents
                    convo_metadata[convo_id]['agents'] = agents_list

        # Step 4: Add metadata to each conversation
        result = {}
        for convo_id in paginated_convo_ids:
            if convo_id in conversations:
                # Get first user message as title
                title = "Untitled Conversation"
                for msg in conversations[convo_id]:
                    if msg['role'] == 'user':
                        title = msg['content_preview']
                        break

                # Add metadata
                metadata = convo_metadata.get(convo_id, {})
                conversation_data = {
                    'conversation_id': convo_id,
                    'title': title,
                    'is_bad_case': metadata.get('is_bad_case', False),
                    'is_good_case': metadata.get('is_good_case', False),
                    'repair_status': metadata.get('repair_status', 0),
                    'repair_note': metadata.get('repair_note'),
                    'username': metadata.get('username', ''),
                    'email': metadata.get('email', ''),
                    'last_message_time': metadata.get('last_message_time'),
                    'max_time_spend': metadata.get('max_time_spend'),  # 添加该对话的最大time_spend
                    'agents': metadata.get('agents', []),
                    'messages': conversations[convo_id]
                }
                
                # Add department information if available
                if 'first_level_department' in metadata:
                    conversation_data['first_level_department'] = metadata['first_level_department']
                
                result[convo_id] = conversation_data

        logger.info(f"Dashboard query returned {len(result)} conversations")
        return result

    except Error as e:
        # Error already logged
        return {}
    except Exception as e:
        logger.error(f"Unexpected error querying dashboard conversations: {e}", exc_info=True)
        return {}
    finally:
        if cursor:
            cursor.close()
        if conn and conn.is_connected():
            conn.close()


def _build_where_clauses_from_filters(filters: Dict[str, Any]) -> Tuple[List[str], List[Any]]:
    """
    Build WHERE clauses and parameters from filters.

    Args:
        filters (Dict[str, Any]): Filters to apply to the query.

    Returns:
        Tuple[List[str], List[Any]]: A tuple of (where_clauses, params)
    """
    where_clauses = []
    params = []

    # Username filter
    if filters.get('username'):
        where_clauses.append("ch.username = %s")
        params.append(str(filters['username']))

    # Email filter
    if filters.get('email'):
        where_clauses.append("ch.email = %s")
        params.append(str(filters['email']))

    # Role filter
    if filters.get('role'):
        where_clauses.append("ch.role = %s")
        params.append(str(filters['role']))

    # Start date filter
    if filters.get('start_date'):
        try:
            # Assume start_date is in 'YYYY-MM-DD' format, need millisecond timestamp for 00:00:00 of that day
            start_dt = datetime.strptime(str(filters['start_date']), '%Y-%m-%d')
            start_timestamp = int(start_dt.timestamp() * 1000)
            where_clauses.append("ch.timestamp >= %s")
            params.append(start_timestamp)
        except (ValueError, TypeError):
            logger.warning(f"Invalid start_date format or type: {filters['start_date']}")

    # End date filter
    if filters.get('end_date'):
        try:
            # Assume end_date is in 'YYYY-MM-DD' format, need millisecond timestamp for 23:59:59.999 of that day
            end_dt = datetime.strptime(str(filters['end_date']) + " 23:59:59", '%Y-%m-%d %H:%M:%S')
            end_timestamp = int(end_dt.timestamp() * 1000) + 999  # Include milliseconds
            where_clauses.append("ch.timestamp <= %s")
            params.append(end_timestamp)
        except (ValueError, TypeError):
            logger.warning(f"Invalid end_date format or type: {filters['end_date']}")

    # Content search filter
    if filters.get('content_search'):
        where_clauses.append("ch.content LIKE %s")
        params.append(f"%{str(filters['content_search'])}%")

    # Conversation ID filter
    if filters.get('conversation_id'):
        where_clauses.append("ch.conversation_id = %s")
        params.append(str(filters['conversation_id']))

    # Agent filter - 支持多agent字段（逗号分隔）
    if filters.get('agent'):
        agent_name = str(filters['agent']).strip()
        # 使用LIKE查询来匹配包含指定agent的记录
        # 支持：单独的agent、开头的agent、中间的agent、结尾的agent
        where_clauses.append(
            "(ch.agent = %s OR ch.agent LIKE %s OR ch.agent LIKE %s OR ch.agent LIKE %s)"
        )
        params.extend([
            agent_name,                    # 完全匹配
            f"{agent_name},%",            # 开头匹配
            f"%,{agent_name},%",          # 中间匹配
            f"%,{agent_name}"             # 结尾匹配
        ])

    # First level department filter - 一级部门筛选
    if filters.get('first_level_department'):
        department = str(filters['first_level_department']).strip()
        # 使用LEFT JOIN user表来获取部门信息，利用idx_first_level_department索引
        where_clauses.append("u.first_level_department = %s")
        params.append(department)

    # Case type filter - supporting bad case, good case, and normal case filtering
    if 'bad_case_filter' in filters and filters['bad_case_filter'] is not None:
        if filters['bad_case_filter'] == 'only_bad':
            where_clauses.append("EXISTS (SELECT 1 FROM bad_case bc WHERE bc.conversation_id = ch.conversation_id)")
        elif filters['bad_case_filter'] == 'only_good_case':
            where_clauses.append("EXISTS (SELECT 1 FROM good_case gc WHERE gc.conversation_id = ch.conversation_id)")
        elif filters['bad_case_filter'] == 'only_normal':
            # Normal cases: not marked as bad case and not marked as good case
            where_clauses.append("NOT EXISTS (SELECT 1 FROM bad_case bc WHERE bc.conversation_id = ch.conversation_id) AND NOT EXISTS (SELECT 1 FROM good_case gc WHERE gc.conversation_id = ch.conversation_id)")

    # Repair status filter
    if 'repair_status_filter' in filters and filters['repair_status_filter'] is not None:
        repair_status = filters['repair_status_filter']
        if repair_status in [0, 1, 2]:
            where_clauses.append("EXISTS (SELECT 1 FROM bad_case bc WHERE bc.conversation_id = ch.conversation_id AND bc.repair_status = %s)")
            params.append(repair_status)

    # Filter out admin users if requested
    if filters.get('filter_admin'):
        # 使用数据库中的 is_admin 字段来过滤管理员用户
        # 需要在查询中添加 LEFT JOIN user 表
        where_clauses.append("NOT EXISTS (SELECT 1 FROM user u WHERE u.email = ch.email AND u.is_admin = 1)")

    return where_clauses, params
