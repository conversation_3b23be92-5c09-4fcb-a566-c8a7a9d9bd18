#!/usr/bin/env python3
"""
检查特定对话ID的数据库记录
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.db.connection import execute_db_query, Database
from src.utils.logger import logger


def check_conversation_records(conversation_id: str):
    """检查指定对话ID的所有记录"""
    
    print(f"🔍 检查对话ID: {conversation_id}")
    print("=" * 60)
    
    # 查询chat_history表中的记录
    sql = """
        SELECT id, username, email, conversation_id, role, 
               CHAR_LENGTH(content) as content_length,
               CHAR_LENGTH(logs) as logs_length,
               agent, time_spend, is_in_process, timestamp,
               created_at, updated_at
        FROM chat_history 
        WHERE conversation_id = %s
        ORDER BY timestamp ASC
    """
    
    try:
        records = execute_db_query(sql, (conversation_id,), fetch='all', database=Database.CHATBI)
        
        if not records:
            print("❌ 没有找到任何记录！")
            return False
            
        print(f"✅ 找到 {len(records)} 条记录:")
        print()
        
        for i, record in enumerate(records, 1):
            print(f"记录 {i}:")
            print(f"  ID: {record['id']}")
            print(f"  用户: {record['username']} ({record['email']})")
            print(f"  角色: {record['role']}")
            print(f"  内容长度: {record['content_length']} 字符")
            print(f"  日志长度: {record['logs_length']} 字符")
            print(f"  Agent: {record['agent']}")
            print(f"  耗时: {record['time_spend']} 秒")
            print(f"  处理状态: {'处理中' if record['is_in_process'] else '已完成'}")
            print(f"  时间戳: {record['timestamp']}")
            print(f"  创建时间: {record['created_at']}")
            print(f"  更新时间: {record['updated_at']}")
            print()
            
        # 检查是否有未完成的记录
        incomplete_records = [r for r in records if r['is_in_process'] == 1]
        if incomplete_records:
            print(f"⚠️  发现 {len(incomplete_records)} 条未完成的记录（is_in_process=1）")
            for record in incomplete_records:
                print(f"  - ID {record['id']}: {record['role']} 记录未完成")
        
        # 检查assistant记录的完整性
        assistant_records = [r for r in records if r['role'] == 'assistant']
        if assistant_records:
            print(f"🤖 Assistant记录分析:")
            for record in assistant_records:
                issues = []
                if not record['content_length']:
                    issues.append("内容为空")
                if not record['logs_length']:
                    issues.append("日志为空")
                if not record['agent']:
                    issues.append("Agent信息缺失")
                if record['time_spend'] is None:
                    issues.append("耗时信息缺失")
                    
                if issues:
                    print(f"  - ID {record['id']}: ❌ {', '.join(issues)}")
                else:
                    print(f"  - ID {record['id']}: ✅ 记录完整")
        
        return True
        
    except Exception as e:
        print(f"❌ 查询出错: {e}")
        logger.exception(f"查询对话记录时出错: {e}")
        return False


def check_streaming_records(conversation_id: str):
    """检查是否有遗留的流式记录"""
    
    print("\n🔄 检查流式记录状态:")
    print("-" * 40)
    
    # 查询所有is_in_process=1的记录
    sql = """
        SELECT id, role, CHAR_LENGTH(content) as content_length, 
               created_at, updated_at, timestamp
        FROM chat_history 
        WHERE conversation_id = %s AND is_in_process = 1
        ORDER BY timestamp DESC
    """
    
    try:
        streaming_records = execute_db_query(sql, (conversation_id,), fetch='all', database=Database.CHATBI)
        
        if not streaming_records:
            print("✅ 没有遗留的流式记录")
            return True
            
        print(f"⚠️  发现 {len(streaming_records)} 条遗留的流式记录:")
        for record in streaming_records:
            print(f"  - ID {record['id']}: {record['role']}, 内容长度: {record['content_length']}")
            print(f"    创建: {record['created_at']}, 更新: {record['updated_at']}")
            
        return False
        
    except Exception as e:
        print(f"❌ 查询流式记录出错: {e}")
        return False


def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python check_conversation.py <conversation_id>")
        sys.exit(1)
        
    conversation_id = sys.argv[1]
    
    # 检查记录
    has_records = check_conversation_records(conversation_id)
    
    if has_records:
        # 检查流式记录状态
        check_streaming_records(conversation_id)
    
    print("\n" + "=" * 60)
    if has_records:
        print("✅ 检查完成，对话记录存在")
    else:
        print("❌ 检查完成，对话记录不存在或有问题")


if __name__ == "__main__":
    main()
