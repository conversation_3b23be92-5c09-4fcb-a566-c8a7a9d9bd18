# CLAUDE.md

此文件为 Claude Code（claude.ai/code）提供本代码库的专属指导。

## 快速上手

### 环境搭建
```bash
# 一次性环境初始化
chmod +x setup_dev_env.sh
./setup_dev_env.sh

# 配置环境变量
cp env.example .env
# 编辑 .env 文件，配置飞书应用ID/密钥和数据库连接信息

# 启动数据库
docker-compose up -d mysql
```

### 日常开发命令
```bash
# 启动/重启应用
./restart.sh --debug              # 调试模式启动
./restart.sh                      # 生产模式启动

# 查看日志
./restart.sh && tail -f ../logs/chat_bi_mysql.log

# 依赖管理
uv sync                           # 同步依赖包
uv pip install <包名>             # 安装新包
```

### 开发工具链
```bash
# 代码格式化
uv run black src/ tests/

# 代码检查
uv run flake8 src/ tests/

# 类型检查
uv run mypy src/
```

## 系统架构

### 核心组件
- **CoordinatorBot**: 主协调器，将用户查询路由到专业分析工具
- **DataFetcherBot**: 数据获取专家，执行SQL查询和数据分析
- **Agent-as-Tool模式**: 通过配置将专业代理封装为可重用工具

### 技术栈
- **后端**: Flask + Python 3.9+
- **AI框架**: Agents库支持多种LLM模型
- **数据库**: 双MySQL架构（ChatBI系统库 + 业务数据仓）
- **集成平台**: 飞书机器人 + WebSocket实时通信
- **认证系统**: JWT + 飞书OAuth2.0三重认证

### 目录结构
```
src/
├── api/                     # REST API端点（认证、查询、看板）
├── services/
│   ├── agent/              # 代理系统（协调器、数据获取器）
│   ├── auth/               # 认证与会话管理
│   ├── feishu/             # 飞书集成与消息处理
│   └── xianmudb/           # 业务数据库操作层
├── static/                 # 前端资源（JS/React）
├── templates/              # HTML模板文件
└── utils/                  # 通用工具与助手函数
```

## Agent开发指南

### 新增专业分析工具
1. 在 `resources/data_fetcher_bot_config/` 创建 `.yml` 配置文件
2. 在 `resources/prompt/` 添加对应的描述 `.md` 文件
3. 在 `coordinator_bot.yml` 的 `agent_tools` 中注册新Agent
4. 如需自定义工具，添加到 `src/services/agent/tools/`

### 配置文件位置
- **Agent配置**: `resources/data_fetcher_bot_config/*.yml`
- **系统提示词**: `resources/prompt/`
- **工具定义**: `src/services/agent/tools/`
- **表结构DDL**: `resources/tables_ddl/`

## 数据库操作规范

### 数据源说明
- **ChatBI库**: 系统数据（用户、会话、历史记录）- 端口3307
- **业务库**: 分析数据（订单、商户、报表）- 端口3306

### 安全查询模式
```python
from src.services.xianmudb.query_service import execute_business_query_async

# 参数化安全查询
result = await execute_business_query_async(
    "SELECT * FROM orders WHERE id = %s", 
    params=(order_id,)
)
```

## API开发规范

### 新增API端点
1. 在 `src/api/` 创建新的API文件
2. 受保护路由使用 `@login_required` 装饰器
3. 在 `src/api/__init__.py` 中注册
4. 确保添加到 `register_routes()` 函数

### 通用开发模式
- **认证逻辑**: 参考 `user_login_with_feishu.py`
- **响应格式**: 遵循现有JSON响应规范
- **异常处理**: 使用统一异常处理器

## 测试指南

### Agent查询测试
```python
# 测试Agent分析查询
curl -X POST http://localhost:5700/api/query \
  -H "Content-Type: application/json" \
  -d '{"message": "展示月度销售趋势"}'
```

### 飞书Webhook测试
- 本地调试：`http://localhost:5700/feishu/webhook`
- 外网测试：使用ngrok创建公网地址进行飞书回调测试

## 端口配置
- **应用服务**: 5700端口
- **MySQL数据库**: 3307（容器的3306映射端口）
- **日志文件**: `../logs/chat_bi_mysql.log`

## 核心环境变量
- `APPLICATION_ROOT`: URL路径前缀
- `ENABLE_BOT_MESSAGE_PROCESSING`: 飞书机器人启用开关
- `FEISHU_APP_ID`, `FEISHU_APP_SECRET`: 飞书应用凭证
- ChatBI库和业务库连接字符串配置

## 故障排查
- **认证问题**: 检查JWT Token有效性及Session过期状态
- **Agent超时**: 监控数据库连接池状态，优化SQL查询性能
- **飞书无响应**: WebSocket连接状态检查，验证飞书应用配置