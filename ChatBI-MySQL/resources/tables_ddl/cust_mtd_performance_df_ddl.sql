CREATE TABLE `xianmu_offline_db`.`cust_mtd_performance_df` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键、自增',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_bd_name` varchar(20) DEFAULT NULL COMMENT '最新归属BD姓名, 取自`xianmudb`.`crm_bd_org`表的bd_name',
  `last_bd_id` bigint DEFAULT NULL COMMENT 'BD_ID, 取自`xianmudb`.`crm_bd_org`表的bd_id',
  `bd_region` varchar(20) DEFAULT NULL COMMENT '销售大区, 这是指BD所在的团队的M2主管所负责的团队名字，枚举值[上海大区, 华中大区, 华南大区, 山东大区, 昆明大区, 浙江大区, 苏皖大区, 西南大区, 闽桂大区]',
  `bd_work_zone` varchar(20) DEFAULT NULL COMMENT 'BD所在的销售区域',
  `cust_id` bigint DEFAULT NULL COMMENT '客户ID, 取自`xianmudb`.`merchant`表的m_id',
  `last_cust_name` varchar(128) DEFAULT NULL COMMENT '客户名称, 取自`xianmudb`.`merchant`表的mname',
  `cust_dlv_type` varchar(32) DEFAULT NULL COMMENT '客户是否是A类客户, 枚举值[A, 非A]',
  `total_score_num` decimal(10,2) DEFAULT NULL COMMENT 'BD利润积分累计',
  `bd_performance_rate` decimal(10,2) DEFAULT NULL COMMENT '利润积分系数',
  `dlv_cust_cnt` bigint DEFAULT NULL COMMENT '客户本月是否有履约，1或者0',
  `cust_comm_amt` decimal(10,2) DEFAULT NULL COMMENT '客户达到高价值客户门槛后的BD一次性佣金，和履约GMV无关',
  `dlv_ori_amt` decimal(10,2) DEFAULT NULL COMMENT '客户本月截止目前的 履约应付GMV',
  `dlv_real_amt` decimal(10,2) DEFAULT NULL COMMENT '客户本月截止目前的 履约实付GMV',
  `item_profit_amt` decimal(10,2) DEFAULT NULL COMMENT '客户本月截止目前的 自营商品毛利润',
  `dlv_real_amt_at` decimal(10,2) DEFAULT NULL COMMENT '客户本月截止目前的 AT_履约实付金额',
  `dlv_real_amt_expo` decimal(10,2) DEFAULT NULL COMMENT '客户本月截止目前的 流量品_履约实付金额',
  `dlv_real_amt_profit` decimal(10,2) DEFAULT NULL COMMENT '客户本月截止目前的 利润品_履约实付金额',
  `dlv_real_amt_normal` decimal(10,2) DEFAULT NULL COMMENT '客户本月截止目前的 常规品_履约实付金额',
  `dlv_real_amt_fruit` decimal(10,2) DEFAULT NULL COMMENT '客户本月截止目前的 鲜果_履约实付金额',
  `cate_group_score_num` decimal(10,2) DEFAULT NULL COMMENT '本月该客户的类目利润得分',
  `dlv_spu_cnt` bigint DEFAULT NULL COMMENT '本月该客户的履约SPU数',
  `more_than_spu_cnt` bigint DEFAULT NULL COMMENT '本月该客户超额SPU数（每个大区都有一个标准，超过该门槛则销售有额外奖励）',
  `more_than_spu_comm` decimal(10,2) DEFAULT NULL COMMENT '超额SPU数的佣金，其实就是more_than_spu_cnt的佣金结果',
  `more_than_spu_cust` bigint DEFAULT NULL COMMENT '本月该客户是否超额SPU，1或者0',
  `total_comm_amt` decimal(10,2) DEFAULT NULL COMMENT '客户达到高价值客户门槛后的佣金汇总，和履约GMV有关',
  `is_test_bd` varchar(10) DEFAULT NULL COMMENT '是否测试BD',
  `data_month` varchar(6) GENERATED ALWAYS AS (substr(`ds`,1,6)) STORED COMMENT '数据月份 (yyyyMM)，表示数据统计的月份，如202506，请你总是使用这个字段来查询，不可使用ds字段。',
  `cust_value_lable` varchar(64) DEFAULT NULL COMMENT '高价值客户标签, 枚举值[普通客户, 准高价值客户, 潜力高价值客户, 高价值客户]',
  PRIMARY KEY (`id`),
  KEY `idx_cust_mtd_performance_last_cust_name` (`last_cust_name`),
  KEY `idx_cust_mtd_performance_bd_value_label` (`last_bd_id`,`cust_value_lable`),
  KEY `idx_cust_mtd_performance_bd_cust_value_label` (`last_bd_name`,`cust_value_lable`),
  KEY `idx_cust_mtd_performance_data_month` (`data_month`,`last_bd_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2202594 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='当月BD的每个客户的绩效表现，含BD的利润积分、高价值客户数量、履约GMV、自营商品毛利润、类目利润得分、SPU数、超额SPU数、超额SPU数的佣金、总佣金等信息，用于BD绩效考核、佣金计算和业务分析'