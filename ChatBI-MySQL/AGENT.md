# AGENT.md - AI Agent Development Guidelines

## Overview

This document provides comprehensive guidelines for developing AI agents in the ChatBI-MySQL system, focusing on software engineering best practices including **KISS (Keep It Simple, Stupid)**, **DRY (Don't Repeat Yourself)**, **SOLID principles**, and **DDD (Domain-Driven Design)**.

## Architecture Overview

The ChatBI-MySQL system uses an **Agent-as-Tool** architecture pattern, where specialized agents are encapsulated as reusable tools coordinated by a central `CoordinatorBot`. This approach provides:

- **High cohesion**: Each agent has a single, well-defined responsibility
- **Low coupling**: Agents communicate through standardized interfaces
- **Reusability**: Agents can be composed into different workflows
- **Scalability**: New agents can be added without modifying existing code

## Core Design Principles

### 1. KISS (Keep It Simple, Stupid)

**Principle**: Each component should do one thing well and avoid unnecessary complexity.

**Application in ChatBI**:

#### ✅ Good Examples
```python
# Simple, focused tool function
async def fetch_mysql_sql_result(sql: str, description: str) -> Tuple[SQLQueryResult, str]:
    """Execute SQL and return results - single responsibility"""
    # Only handles SQL execution, nothing else
    return result, description

# Clean agent configuration
# coordinator_bot.yml
agent_name: coordinator_bot
tools:
  - name: search_product_by_name  # One tool per specific task
agent_tools:
  - name: sales_order_analytics   # Each agent has clear domain
```

#### ❌ Bad Examples to Avoid
```python
# Avoid: Complex multi-purpose functions
async def fetch_and_analyze_and_export_and_notify(sql: str, user_email: str, 
                                                analysis_type: str, export_format: str):
    # Too many responsibilities in one function
    pass

# Avoid: Overly complex configuration
tools:
  - name: universal_tool_that_does_everything
    parameters: [sql, analysis, export, notify, visualize, predict]
```

### 2. DRY (Don't Repeat Yourself)

**Principle**: Every piece of knowledge must have a single, unambiguous representation.

**Application in ChatBI**:

#### ✅ Good Examples
```python
# Centralized tool registration
# tool_manager.py
class ToolManager:
    def register_as_function_tool(self, tool_func: Callable) -> FunctionTool:
        """Single source of truth for tool registration"""
        self._tools[tool_func.__name__] = tool_func
        return tool_func

# Reusable base class
# base_bot.py
class BaseBot(ABC):
    def get_user_realtime_instruction(self) -> str:
        """Shared instruction generation across all bots"""
        return self._generate_context_aware_instruction()
```

#### ❌ Bad Examples to Avoid
```python
# Avoid: Duplicate tool registration
# data_tools.py
tool_manager.register(fetch_mysql_sql_result)  # Good

# Avoid: Repeating similar code in multiple bots
class SalesBot:
    def get_instruction(self):
        return "You are sales expert..."  # Duplicate
        
class WarehouseBot:
    def get_instruction(self):
        return "You are warehouse expert..."  # Duplicate
```

### 3. SOLID Principles

#### S - Single Responsibility Principle (SRP)

**Each class/agent should have one reason to change.**

**Current Architecture Analysis**:
- ✅ `DataFetcherBot`: Only handles data fetching via SQL
- ✅ `CoordinatorBot`: Only coordinates between specialized agents
- ✅ `ToolManager`: Only manages tool registration and retrieval
- ✅ Each tool function: Single specific purpose

**Implementation Guidelines**:
```python
# Good: Single responsibility per agent
class SalesOrderAnalyticsBot(DataFetcherBot):
    """Only handles sales order analysis"""
    pass

class WarehouseBot(DataFetcherBot):
    """Only handles warehouse operations"""
    pass
```

#### O - Open/Closed Principle (OCP)

**Software entities should be open for extension but closed for modification.**

**Current Architecture Analysis**:
- ✅ **Configuration-driven agents**: New agents added via YAML files
- ✅ **Plugin-based tools**: New tools registered without modifying core
- ✅ **Inheritance-based extension**: `DataFetcherBot` extended for new domains

**Extension Examples**:
```yaml
# Adding new agent without code changes
# new_agent.yml
agent_name: inventory_analytics
agent_description: inventory_analytics.md
tools:
  - name: fetch_mysql_sql_result  # Reuse existing tools
agent_tables:
  - name: inventory
```

#### L - Liskov Substitution Principle (LSP)

**Subtypes must be substitutable for their base types.**

**Current Implementation**:
```python
# BaseBot defines contract that all bots must fulfill
class BaseBot(ABC):
    @abstractmethod
    def create_agent(self) -> Agent:
        """All bots must provide agent creation"""
        
# Any BaseBot subclass can replace another
coordinator = CoordinatorBot(user_info)
sales_bot = SalesOrderAnalyticsBot(user_info)

# Both satisfy BaseBot contract
assert isinstance(coordinator, BaseBot)
assert isinstance(sales_bot, BaseBot)
```

#### I - Interface Segregation Principle (ISP)

**Clients should not be forced to depend on interfaces they don't use.**

**Current Architecture**:
- ✅ **Focused interfaces**: Each tool has minimal, specific parameters
- ✅ **Role-based configuration**: Agents only load tools they need

```python
# Good: Minimal tool interfaces
async def get_table_sample_data(table_name: str, sample_size: int = 2) -> SQLQueryResult:
    """Only what the tool needs, nothing more"""
    
# Good: Agent-specific tool selection
# sales_order_analytics.yml
tools:
  - name: fetch_mysql_sql_result  # Only sales-related tools
  - name: get_table_sample_data
```

#### D - Dependency Inversion Principle (DIP)

**High-level modules should not depend on low-level modules. Both should depend on abstractions.**

**Current Architecture**:
- ✅ **Abstract BaseBot**: High-level coordination depends on abstraction
- ✅ **Configuration-driven**: Dependencies injected via YAML
- ✅ **Tool abstraction**: Agents depend on tool interfaces, not implementations

```python
# Good: Dependency injection via configuration
class DataFetcherBot(BaseBot):
    def __init__(self, user_info: Dict[str, Any], config_file: str):
        self.config = _load_config(config_file)  # Config injects dependencies
        
# Good: Tool interface abstraction
class ToolManager:
    def get_tool(self, tool_name: str) -> Optional[FunctionTool]:
        """Returns tool interface, not implementation"""
```

### 4. DDD (Domain-Driven Design)

#### Bounded Contexts

The system has clear bounded contexts:

1. **Sales Domain** (`sales_order_analytics`)
   - Bounded by sales operations, orders, customers
   - Owns sales-related tables and business rules

2. **Warehouse Domain** (`warehouse_and_fulfillment`)
   - Bounded by inventory, logistics, fulfillment
   - Owns warehouse operations and supply chain

3. **Coordination Domain** (`coordinator_bot`)
   - Bounded by user interaction and agent orchestration
   - Owns conversation flow and tool selection

#### Ubiquitous Language

**Consistent terminology across the codebase**:

- **Agent**: Specialized AI assistant for a domain
- **Tool**: Reusable function that performs specific operations
- **Bot**: Configuration wrapper for an agent
- **Coordinator**: Central orchestrator for multiple agents

#### Domain Models

```yaml
# sales_order_analytics.yml - Sales Domain Model
agent_tables:
  - name: orders
    desc: "订单表，记录所有订单的基本信息..."  # Domain-specific language
  - name: merchant
    desc: "商户信息主表..."  # Business terminology
```

## Best Practices for Agent Development

### 1. Agent Design Guidelines

#### Create Focused Agents
```yaml
# Good: Single-domain agent
agent_name: sales_order_analytics
agent_description: sales_order_analytics.md  # Detailed domain knowledge
agent_tables:
  - name: orders      # Only sales-related tables
  - name: merchant    # Only sales-relevant merchant data
```

#### Use Configuration Over Code
```yaml
# Good: Configuration-driven
model: google/gemini-2.5-pro
model_settings:
  temperature: 0.1
  extra_body: {"provider": {"sort": "throughput"}}
```

### 2. Tool Development Guidelines

#### Design Reusable Tools
```python
# Good: Generic, reusable tool
async def fetch_mysql_sql_result(
    sql: str, 
    description: str, 
    database: str = "business"
) -> Tuple[SQLQueryResult, str]:
    """Generic SQL execution - can be used by any agent"""
    
# Good: Parameterized for flexibility
def get_table_sample_data(table_name: str, sample_size: int = 2) -> SQLQueryResult:
    """Flexible sampling tool"""
```

#### Avoid Agent-Specific Tools
```python
# Bad: Tool tied to specific agent
async def get_sales_specific_customer_data_only_for_sales_bot():
    """Too specific - limits reusability"""
```

### 3. Configuration Management

#### Use YAML for Agent Definition
```yaml
# warehouse_and_fulfillment.yml
agent_name: warehouse_and_fulfillment
model_provider: openrouter
model: google/gemini-2.5-pro
model_settings:
  temperature: 0.5
  top_p: 0.9
tools:
  - name: fetch_mysql_sql_result
  - name: get_table_sample_data
  - name: fetch_ddl_for_table
need_system_prompt: false
agent_description: warehouse_and_fulfillment.md
agent_tables:
  - name: warehouse_storage_center
  - name: inventory
```

#### Centralize Shared Configuration
```python
# Good: Shared model provider
# model_provider.py
CACHE_ENABLED_LITE_LLM_MODEL = CacheEnabledLitellmModel(
    model=f"openai/{OPENAI_MODEL}",
    api_key=OPENAI_API_KEY,
    base_url=OPENAI_API_BASE
)
```

### 4. Error Handling and Resilience

#### Graceful Degradation
```python
# Good: Safe model selection with provider support
def _safe_get_model(self, config_model: str, fallback_model: Optional[Model]) -> Model:
    """Safe model selection with fallback"""
    try:
        # 从配置中获取model_provider，如果没有则使用默认provider
        config_provider = self.config.get("model_provider")
        return get_model_for_name(config_model, config_provider)
    except Exception as e:
        logger.warning(f"配置模型失败: {e}，使用默认模型")
        return fallback_model or CACHE_ENABLED_CLAUDE_MODEL
```

#### Comprehensive Logging
```python
# Good: Detailed logging for debugging
logger.info(
    f"协调者Bot配置: agent_name={agent_name}, "
    f"tools={len(tools)}, config_model={config_model}"
)
```

## Anti-Patterns to Avoid

### 1. God Objects
```python
# Bad: Agent that does everything
class UniversalBot(BaseBot):
    """Handles sales, warehouse, customer service, analytics..."""
    # Violates SRP and becomes unmaintainable
```

### 2. Tight Coupling
```python
# Bad: Direct dependencies
class SalesBot:
    def __init__(self):
        self.warehouse_bot = WarehouseBot()  # Direct coupling
        self.customer_bot = CustomerBot()    # Hard to test/maintain
```

### 3. Configuration in Code
```python
# Bad: Hard-coded values
class SalesBot:
    def __init__(self):
        self.model = "google/gemini-2.5-pro"  # Hard-coded
        self.temperature = 0.1               # Not configurable
```

### 4. Duplicate Business Logic
```python
# Bad: Repeated SQL generation
class SalesBot:
    def get_sales_query(self):
        sql = "SELECT * FROM orders WHERE..."  # Duplicate
        
class WarehouseBot:
    def get_inventory_query(self):
        sql = "SELECT * FROM orders WHERE..."  # Same logic
```

## Testing Guidelines

### 1. Unit Testing for Tools
```python
# Good: Testable tool design
async def test_fetch_mysql_sql_result():
    result = await fetch_mysql_sql_result(
        sql="SELECT 1 as test",
        description="Test query"
    )
    assert result.success
```

### 2. Configuration Testing
```python
# Good: Validate agent configurations
def validate_agent_configs():
    """Ensure all agent configs are valid"""
    for config_file in list_configs():
        config = load_config(config_file)
        assert "agent_name" in config
        assert "tools" in config
```

### 3. Integration Testing
```python
# Good: Test agent coordination
async def test_coordinator_with_multiple_agents():
    coordinator = CoordinatorBot(test_user_info)
    result = await coordinator.process_query(
        "Show me sales and warehouse data"
    )
    assert "sales_order_analytics" in result.used_agents
    assert "warehouse_and_fulfillment" in result.used_agents
```

## Migration and Evolution Strategies

### 1. Adding New Agents

1. **Create YAML configuration**:
   ```yaml
   # new_analytics_agent.yml
   agent_name: new_analytics_agent
   model_provider: openrouter
   model: google/gemini-2.5-pro
   model_settings:
     temperature: 0.1
   agent_description: new_analytics_agent.md
   tools:
     - name: fetch_mysql_sql_result
   agent_tables:
     - name: relevant_table
   ```

2. **Create description file**:
   ```markdown
   # new_analytics_agent.md
   This agent specializes in [specific domain]...
   ```

3. **Update coordinator configuration**:
   ```yaml
   # coordinator_bot.yml
   agent_tools:
     - name: new_analytics_agent
   ```

### 2. Refactoring Existing Agents

1. **Extract common functionality**:
   ```python
   # Good: Extract shared logic
   class BaseDataFetcherBot(BaseBot):
       """Common data fetching functionality"""
   ```

2. **Use composition over inheritance**:
   ```python
   # Good: Compose behaviors
   class SpecializedBot(BaseBot):
       def __init__(self):
           self.data_fetcher = BaseDataFetcherBot()
           self.analyzer = DomainSpecificAnalyzer()
   ```

## Performance Optimization

### 1. Caching Strategy
```python
# Good: Cache model instances
CACHE_ENABLED_LITE_LLM_MODEL = CacheEnabledLitellmModel(...)
```

### 2. Resource Management
```python
# Good: Connection pooling
user_query_limiter.execute_with_limit(
    user_id=user_id,
    query_func=execute_database_query_async
)
```

### 3. Query Optimization
```python
# Good: Efficient SQL generation
def generate_optimized_query(tables, conditions):
    """Generate efficient queries based on table relationships"""
```

## Monitoring and Observability

### 1. Structured Logging
```python
logger.info(
    "agent_execution",
    extra={
        "agent_name": agent_name,
        "user_id": user_id,
        "query_type": query_type,
        "duration_ms": duration
    }
)
```

### 2. Performance Metrics
- Agent execution time
- Tool usage frequency
- Error rates by agent
- User satisfaction scores

## Summary

The ChatBI-MySQL system demonstrates excellent application of software engineering principles:

- **KISS**: Each agent has a single, clear responsibility
- **DRY**: Shared tools and base classes prevent duplication
- **SOLID**: Well-structured inheritance and composition
- **DDD**: Clear bounded contexts and ubiquitous language

By following these guidelines, new agents can be added efficiently while maintaining system integrity and performance.