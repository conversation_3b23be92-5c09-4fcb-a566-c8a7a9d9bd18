# 用户一级部门功能部署指南

## 部署步骤

### 1. 数据库迁移

首先执行数据库迁移脚本，为user表添加一级部门字段：

```bash
# 连接到MySQL数据库
mysql -u your_username -p chatbi

# 执行迁移脚本
source migrations/add_first_level_department.sql
```

或者直接执行SQL：

```sql
USE chatbi;

-- 在user表中添加一级部门字段
ALTER TABLE `user` 
ADD COLUMN `first_level_department` varchar(128) DEFAULT NULL COMMENT '一级部门名称' 
AFTER `job_title`;

-- 添加索引以便查询
ALTER TABLE `user` 
ADD INDEX `idx_first_level_department` (`first_level_department`);
```

### 2. 验证飞书API权限

确保你的飞书应用具有以下权限：

- `contact:user.base:readonly` - 读取用户基本信息
- `contact:department.base:readonly` - 读取部门基本信息

### 3. 重启应用

重启ChatBI应用以加载新的代码：

```bash
# 如果使用uv运行
pkill -f "uv run app.py"
uv run app.py

# 或者如果使用其他方式运行
# 停止现有进程并重新启动
```

### 4. 验证功能

#### 方法1：使用测试脚本

```bash
cd ChatBI-MySQL
python test_department_update.py
```

#### 方法2：检查日志

观察应用日志，当Token刷新服务运行时，应该能看到类似的日志：

```
INFO 成功获取用户一级部门: open_id=ou_xxx, first_level_department=产品技术部
INFO 用户一级部门更新成功: open_id=ou_xxx, first_level_department=产品技术部
INFO 成功更新用户一级部门: open_id=ou_xxx
```

#### 方法3：检查数据库

查询数据库验证一级部门字段是否被正确更新：

```sql
SELECT open_id, name, email, job_title, first_level_department 
FROM user 
WHERE first_level_department IS NOT NULL 
LIMIT 10;
```

## 功能说明

### 自动触发时机

- Token刷新服务每5分钟检查一次即将过期的token
- 当成功刷新用户token时，会自动更新该用户的一级部门信息

### 获取逻辑

1. 从用户信息中获取第一个部门ID
2. 调用飞书API获取该部门的所有父级部门
3. 取最顶级的父级部门作为一级部门
4. 如果没有父级部门，则当前部门就是一级部门

### 错误处理

- 部门信息获取失败不会影响Token刷新的主要功能
- 所有错误都会被记录到日志中
- 网络超时或API调用失败会被妥善处理

## 监控和维护

### 日志监控

关注以下日志关键词：

- `成功更新用户一级部门` - 正常更新
- `更新用户一级部门失败` - 更新失败
- `未能获取用户一级部门` - 获取部门信息失败
- `获取部门父级信息失败` - API调用失败

### 数据质量检查

定期检查一级部门数据的完整性：

```sql
-- 检查有多少用户没有一级部门信息
SELECT COUNT(*) as users_without_department
FROM user 
WHERE first_level_department IS NULL OR first_level_department = '';

-- 查看一级部门分布
SELECT first_level_department, COUNT(*) as user_count
FROM user 
WHERE first_level_department IS NOT NULL 
GROUP BY first_level_department
ORDER BY user_count DESC;
```

## 故障排除

### 常见问题

1. **权限不足错误**
   - 检查飞书应用的API权限配置
   - 确保tenant token有效

2. **网络连接问题**
   - 检查服务器网络连接
   - 验证防火墙设置

3. **数据库连接问题**
   - 检查数据库连接配置
   - 验证数据库用户权限

### 调试步骤

1. 查看应用日志中的错误信息
2. 使用测试脚本验证API调用
3. 手动执行SQL查询检查数据库状态
4. 检查飞书开发者后台的API调用记录

## 回滚方案

如果需要回滚此功能：

1. **移除数据库字段**（可选）：
```sql
ALTER TABLE `user` DROP COLUMN `first_level_department`;
ALTER TABLE `user` DROP INDEX `idx_first_level_department`;
```

2. **恢复代码**：
   - 恢复到之前的代码版本
   - 重启应用

注意：建议保留数据库字段，只是停用功能，以免丢失已收集的部门数据。
