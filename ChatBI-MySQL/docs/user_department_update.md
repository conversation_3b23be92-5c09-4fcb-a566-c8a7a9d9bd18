# 用户一级部门自动更新功能

## 概述

本功能在现有的Token刷新服务基础上，增加了自动更新用户一级部门信息的能力。当系统刷新用户的access token时，会同时获取并更新用户的一级部门信息到数据库中。

## 功能特性

- **自动更新**：在Token刷新过程中自动更新用户一级部门
- **飞书API集成**：调用飞书部门相关API获取准确的部门层级信息
- **数据库存储**：将一级部门信息存储在user表的first_level_department字段中
- **错误处理**：部门更新失败不会影响Token刷新的主要功能
- **日志记录**：详细的日志记录便于问题排查

## 实现原理

### 1. 获取用户部门信息流程

1. **获取用户信息**：调用飞书用户信息API获取用户的部门列表
2. **提取第一个部门**：从department_ids数组中取第一个部门ID
3. **获取部门父级**：调用飞书部门父级API获取该部门的所有父级部门
4. **确定一级部门**：取父级部门列表中的最后一个作为一级部门

### 2. 数据库结构

在user表中新增字段：
```sql
`first_level_department` varchar(128) DEFAULT NULL COMMENT '一级部门名称'
```

### 3. API调用示例

#### 获取用户信息
```
GET https://open.feishu.cn/open-apis/contact/v3/users/{open_id}
```

#### 获取部门父级信息
```
GET https://open.feishu.cn/open-apis/contact/v3/departments/parent?department_id={department_id}
```

## 核心组件

### 1. DepartmentService (src/services/feishu/department_service.py)

负责飞书部门相关API的调用：

- `get_department_parents(department_id)`: 获取部门的所有父级部门
- `get_user_first_level_department(open_id)`: 获取用户的一级部门名称
- `get_department_info(department_id)`: 获取单个部门的详细信息

### 2. UserService 扩展

在现有的UserService中新增：

- `update_user_first_level_department(open_id)`: 更新用户的一级部门信息

### 3. TokenRefreshService 集成

在Token刷新成功后调用部门更新功能：

- `_update_user_department(open_id)`: 更新用户部门信息的内部方法

### 4. 数据库操作

- `update_user_first_level_department(open_id, first_level_department)`: 更新数据库中的一级部门字段

## 使用方法

### 自动更新

功能会在Token刷新服务运行时自动执行，无需手动干预。

### 手动测试

可以使用提供的测试脚本进行功能验证：

```bash
python test_department_update.py
```

### 数据库迁移

首次部署时需要执行数据库迁移脚本：

```sql
-- 执行 migrations/add_first_level_department.sql
ALTER TABLE `user` 
ADD COLUMN `first_level_department` varchar(128) DEFAULT NULL COMMENT '一级部门名称' 
AFTER `job_title`;

ALTER TABLE `user` 
ADD INDEX `idx_first_level_department` (`first_level_department`);
```

## 配置说明

### 飞书API权限

确保应用具有以下权限：
- `contact:user.base:readonly` - 读取用户基本信息
- `contact:department.base:readonly` - 读取部门基本信息

### 错误处理

- 如果获取部门信息失败，会记录警告日志但不影响Token刷新
- 如果用户没有部门信息，会记录警告日志
- 如果部门没有父级部门，会将当前部门作为一级部门

## 日志示例

```
2025-01-22 10:30:15 INFO 成功获取用户一级部门: open_id=ou_xxx, first_level_department=产品技术部
2025-01-22 10:30:15 INFO 用户一级部门更新成功: open_id=ou_xxx, first_level_department=产品技术部
2025-01-22 10:30:15 INFO 成功更新用户一级部门: open_id=ou_xxx
```

## 注意事项

1. **权限要求**：确保飞书应用具有读取用户和部门信息的权限
2. **网络依赖**：功能依赖飞书API的网络连接
3. **数据一致性**：部门信息会在每次Token刷新时更新，保持数据的时效性
4. **性能影响**：增加了API调用，但不会显著影响Token刷新的性能

## 故障排除

### 常见问题

1. **权限不足**：检查飞书应用的API权限配置
2. **网络超时**：检查网络连接和防火墙设置
3. **部门信息为空**：用户可能没有分配到任何部门

### 调试方法

1. 查看应用日志中的相关错误信息
2. 使用测试脚本验证API调用
3. 检查数据库中的first_level_department字段更新情况
